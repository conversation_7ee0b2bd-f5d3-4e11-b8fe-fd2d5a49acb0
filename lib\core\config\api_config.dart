class ApiConfig {
  // URLs de l'API
  static const String baseUrl = 'https://api.pointage-agents.com';
  static const String apiVersion = 'v1';
  
  // Endpoints
  static const String loginEndpoint = '/auth/login';
  static const String logoutEndpoint = '/auth/logout';
  static const String refreshEndpoint = '/auth/refresh';
  static const String userEndpoint = '/user';
  static const String planningEndpoint = '/planning';
  static const String pointageEndpoint = '/pointage';
  static const String congesEndpoint = '/conges';
  static const String agentsEndpoint = '/agents';
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Headers par défaut
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // Configuration de l'environnement
  static String get fullBaseUrl => '$baseUrl/api/$apiVersion';
  
  // Méthodes utilitaires
  static String buildUrl(String endpoint) {
    return '$fullBaseUrl$endpoint';
  }
  
  static Map<String, String> getHeaders({String? token}) {
    final headers = Map<String, String>.from(defaultHeaders);
    if (token != null && token.isNotEmpty) {
      headers['Authorization'] = 'Bearer $token';
    }
    return headers;
  }
}