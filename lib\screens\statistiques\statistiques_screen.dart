import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../core/providers/pointage_provider.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/models/pointage.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';

class StatistiquesScreen extends StatefulWidget {
  const StatistiquesScreen({super.key});

  @override
  State<StatistiquesScreen> createState() => _StatistiquesScreenState();
}

class _StatistiquesScreenState extends State<StatistiquesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'Mois';
  DateTime _selectedDate = DateTime.now();
  final List<String> _periods = ['<PERSON>ma<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadStatistics();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    final pointageProvider = Provider.of<PointageProvider>(
      context,
      listen: false,
    );
    await pointageProvider.loadPointagesDuJour();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [_buildAppBar(), _buildPeriodSelector(), _buildTabBar()];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(),
            _buildChartsTab(),
            _buildDetailsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Statistiques',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.calendar_today, color: Colors.white),
          onPressed: _showDatePicker,
        ),
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _loadStatistics,
        ),
      ],
    );
  }

  Widget _buildPeriodSelector() {
    return SliverToBoxAdapter(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            const Text(
              'Période:',
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children:
                      _periods.map((period) {
                        final isSelected = _selectedPeriod == period;
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(period),
                            selected: isSelected,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedPeriod = period;
                                });
                                _loadStatistics();
                              }
                            },
                            selectedColor: Theme.of(
                              context,
                            ).primaryColor.withValues(alpha: 0.2),
                            checkmarkColor: Theme.of(context).primaryColor,
                          ),
                        );
                      }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverAppBarDelegate(
        TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
          tabs: const [
            Tab(text: 'Vue d\'ensemble', icon: Icon(Icons.dashboard)),
            Tab(text: 'Graphiques', icon: Icon(Icons.bar_chart)),
            Tab(text: 'Détails', icon: Icon(Icons.list_alt)),
          ],
        ),
      ),
      pinned: true,
    );
  }

  Widget _buildOverviewTab() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        if (pointageProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement des statistiques...'),
          );
        }

        final pointages = _getFilteredPointages(
          pointageProvider.pointagesDuJour,
        );
        final stats = _calculateStats(pointages);

        return RefreshIndicator(
          onRefresh: _loadStatistics,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Résumé de la période
                _buildPeriodSummaryCard(stats),
                const SizedBox(height: 16),

                // Métriques principales
                _buildMainMetricsGrid(stats),
                const SizedBox(height: 16),

                // Tendances
                _buildTrendsCard(stats),
                const SizedBox(height: 16),

                // Performance
                _buildPerformanceCard(stats),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildChartsTab() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        if (pointageProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement des graphiques...'),
          );
        }

        final pointages = _getFilteredPointages(
          pointageProvider.pointagesDuJour,
        );

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Graphique des heures par jour
              _buildHoursPerDayChart(pointages),
              const SizedBox(height: 16),

              // Graphique de ponctualité
              _buildPunctualityChart(pointages),
              const SizedBox(height: 16),

              // Répartition des types de pointage
              _buildPointageTypeChart(pointages),
              const SizedBox(height: 16),

              // Évolution mensuelle
              _buildMonthlyEvolutionChart(pointages),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailsTab() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        if (pointageProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement des détails...'),
          );
        }

        final pointages = _getFilteredPointages(
          pointageProvider.pointagesDuJour,
        );
        final groupedPointages = _groupPointagesByDate(pointages);

        if (groupedPointages.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.analytics,
            title: 'Aucune donnée',
            subtitle: 'Aucun pointage trouvé pour la période sélectionnée.',
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: groupedPointages.length,
          itemBuilder: (context, index) {
            final date = groupedPointages.keys.elementAt(index);
            final dayPointages = groupedPointages[date]!;
            return _buildDayDetailsCard(date, dayPointages);
          },
        );
      },
    );
  }

  Widget _buildPeriodSummaryCard(Map<String, dynamic> stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: Column(
          children: [
            Text(
              _getPeriodTitle(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Jours travaillés',
                    '${stats['joursTravaills']}',
                    Icons.work,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Heures totales',
                    '${stats['heuresTotales']}h',
                    Icons.schedule,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'Moyenne/jour',
                    '${stats['moyenneParJour']}h',
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMainMetricsGrid(Map<String, dynamic> stats) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.2,
      children: [
        _buildMetricCard(
          'Ponctualité',
          '${stats['ponctualite']}%',
          Icons.access_time,
          Colors.green,
          stats['ponctualite'] >= 90 ? Colors.green : Colors.orange,
        ),
        _buildMetricCard(
          'Retards',
          '${stats['retards']}',
          Icons.schedule_outlined,
          Colors.orange,
        ),
        _buildMetricCard(
          'Absences',
          '${stats['absences']}',
          Icons.event_busy,
          Colors.red,
        ),
        _buildMetricCard(
          'Heures sup.',
          '${stats['heuresSupplementaires']}h',
          Icons.add_alarm,
          Colors.blue,
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color, [
    Color? valueColor,
  ]) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: valueColor ?? color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendsCard(Map<String, dynamic> stats) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Tendances',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTrendItem(
              'Heures travaillées',
              stats['tendanceHeures'] ?? 0.0,
              'vs période précédente',
            ),
            const SizedBox(height: 12),
            _buildTrendItem(
              'Ponctualité',
              stats['tendancePonctualite'] ?? 0.0,
              'vs période précédente',
            ),
            const SizedBox(height: 12),
            _buildTrendItem(
              'Productivité',
              stats['tendanceProductivite'] ?? 0.0,
              'vs période précédente',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendItem(String label, double percentage, String subtitle) {
    final isPositive = percentage >= 0;
    final color = isPositive ? Colors.green : Colors.red;
    final icon = isPositive ? Icons.trending_up : Icons.trending_down;

    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
              Text(
                subtitle,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
        Text(
          '${isPositive ? '+' : ''}${percentage.toStringAsFixed(1)}%',
          style: TextStyle(color: color, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildPerformanceCard(Map<String, dynamic> stats) {
    final score = stats['scorePerformance'] ?? 0.0;
    final color = _getPerformanceColor(score);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Score de performance',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getPerformanceLabel(score),
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Barre de progression
            LinearProgressIndicator(
              value: score / 100,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '0',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                Text(
                  '${score.toStringAsFixed(0)}/100',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Text(
                  '100',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Text(
              _getPerformanceDescription(score),
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHoursPerDayChart(List<Pointage> pointages) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Heures par jour',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'Graphique des heures par jour\n(à implémenter avec fl_chart)',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPunctualityChart(List<Pointage> pointages) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ponctualité',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'Graphique de ponctualité\n(à implémenter avec fl_chart)',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPointageTypeChart(List<Pointage> pointages) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Types de pointage',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'Graphique en secteurs\n(à implémenter avec fl_chart)',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlyEvolutionChart(List<Pointage> pointages) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Évolution mensuelle',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Center(
                child: Text(
                  'Graphique d\'évolution\n(à implémenter avec fl_chart)',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayDetailsCard(DateTime date, List<Pointage> pointages) {
    final totalHours = _calculateDayHours(pointages);
    final isLate = _isDayLate(pointages);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        title: Row(
          children: [
            Text(
              DateFormat('EEEE dd/MM/yyyy', 'fr_FR').format(date),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            if (isLate)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'Retard',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Text(
          '${totalHours.toStringAsFixed(1)}h travaillées • ${pointages.length} pointages',
          style: TextStyle(color: Colors.grey[600]),
        ),
        children:
            pointages.map((pointage) {
              return ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getPointageTypeColor(
                      pointage.type,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getPointageTypeIcon(pointage.type),
                    color: _getPointageTypeColor(pointage.type),
                    size: 20,
                  ),
                ),
                title: Text(_getPointageTypeLabel(pointage.type)),
                subtitle: Text(
                  '${pointage.adresse}\n${pointage.commentaire ?? ''}',
                ),
                trailing: Text(
                  DateFormat('HH:mm').format(pointage.dateHeure),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                isThreeLine: pointage.commentaire?.isNotEmpty == true,
              );
            }).toList(),
      ),
    );
  }

  List<Pointage> _getFilteredPointages(List<Pointage> pointages) {
    final now = DateTime.now();
    DateTime startDate;
    DateTime endDate;

    switch (_selectedPeriod) {
      case 'Semaine':
        startDate = now.subtract(Duration(days: now.weekday - 1));
        endDate = startDate.add(const Duration(days: 6));
        break;
      case 'Mois':
        startDate = DateTime(now.year, now.month, 1);
        endDate = DateTime(now.year, now.month + 1, 0);
        break;
      case 'Trimestre':
        final quarter = ((now.month - 1) ~/ 3) + 1;
        startDate = DateTime(now.year, (quarter - 1) * 3 + 1, 1);
        endDate = DateTime(now.year, quarter * 3 + 1, 0);
        break;
      case 'Année':
        startDate = DateTime(now.year, 1, 1);
        endDate = DateTime(now.year, 12, 31);
        break;
      default:
        return pointages;
    }

    return pointages.where((p) {
      return p.dateHeure.isAfter(startDate.subtract(const Duration(days: 1))) &&
          p.dateHeure.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  Map<String, dynamic> _calculateStats(List<Pointage> pointages) {
    if (pointages.isEmpty) {
      return {
        'joursTravaills': 0,
        'heuresTotales': 0.0,
        'moyenneParJour': 0.0,
        'ponctualite': 0.0,
        'retards': 0,
        'absences': 0,
        'heuresSupplementaires': 0.0,
        'scorePerformance': 0.0,
        'tendanceHeures': 0.0,
        'tendancePonctualite': 0.0,
        'tendanceProductivite': 0.0,
      };
    }

    final groupedByDate = _groupPointagesByDate(pointages);
    final joursTravaills = groupedByDate.length;

    double heuresTotales = 0.0;
    int retards = 0;

    for (final dayPointages in groupedByDate.values) {
      heuresTotales += _calculateDayHours(dayPointages);
      if (_isDayLate(dayPointages)) {
        retards++;
      }
    }

    final moyenneParJour =
        joursTravaills > 0 ? heuresTotales / joursTravaills : 0.0;
    final ponctualite =
        joursTravaills > 0
            ? ((joursTravaills - retards) / joursTravaills * 100)
            : 0.0;
    final heuresSupplementaires =
        heuresTotales > (joursTravaills * 8)
            ? heuresTotales - (joursTravaills * 8)
            : 0.0;

    // Score de performance basé sur plusieurs critères
    final scorePerformance = _calculatePerformanceScore(
      ponctualite,
      moyenneParJour,
      retards,
      joursTravaills,
    );

    return {
      'joursTravaills': joursTravaills,
      'heuresTotales': heuresTotales,
      'moyenneParJour': moyenneParJour,
      'ponctualite': ponctualite,
      'retards': retards,
      'absences': 0, // À calculer selon la logique métier
      'heuresSupplementaires': heuresSupplementaires,
      'scorePerformance': scorePerformance,
      'tendanceHeures': 5.2, // Valeurs d'exemple
      'tendancePonctualite': -2.1,
      'tendanceProductivite': 3.8,
    };
  }

  Map<DateTime, List<Pointage>> _groupPointagesByDate(
    List<Pointage> pointages,
  ) {
    final grouped = <DateTime, List<Pointage>>{};

    for (final pointage in pointages) {
      final date = DateTime(
        pointage.dateHeure.year,
        pointage.dateHeure.month,
        pointage.dateHeure.day,
      );
      grouped.putIfAbsent(date, () => []).add(pointage);
    }

    return grouped;
  }

  double _calculateDayHours(List<Pointage> dayPointages) {
    // Logique simplifiée : calculer les heures entre entrée et sortie
    if (dayPointages.length < 2) return 0.0;

    dayPointages.sort((a, b) => a.dateHeure.compareTo(b.dateHeure));
    final firstEntry = dayPointages.first;
    final lastExit = dayPointages.last;

    return lastExit.dateHeure.difference(firstEntry.dateHeure).inMinutes / 60.0;
  }

  bool _isDayLate(List<Pointage> dayPointages) {
    if (dayPointages.isEmpty) return false;

    final firstEntry =
        dayPointages.where((p) => p.type == TypePointage.entree).firstOrNull;
    if (firstEntry == null) return false;

    // Considérer comme retard si arrivée après 9h00
    final expectedTime = DateTime(
      firstEntry.dateHeure.year,
      firstEntry.dateHeure.month,
      firstEntry.dateHeure.day,
      9,
      0,
    );

    return firstEntry.dateHeure.isAfter(expectedTime);
  }

  double _calculatePerformanceScore(
    double ponctualite,
    double moyenneParJour,
    int retards,
    int joursTravaills,
  ) {
    double score = 0.0;

    // Ponctualité (40% du score)
    score += ponctualite * 0.4;

    // Heures travaillées (30% du score)
    final heuresScore = moyenneParJour >= 8 ? 100 : (moyenneParJour / 8 * 100);
    score += heuresScore * 0.3;

    // Assiduité (30% du score)
    final assiduite = joursTravaills >= 20 ? 100 : (joursTravaills / 20 * 100);
    score += assiduite * 0.3;

    return score.clamp(0.0, 100.0);
  }

  Color _getPerformanceColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  String _getPerformanceLabel(double score) {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Bon';
    if (score >= 40) return 'Moyen';
    return 'À améliorer';
  }

  String _getPerformanceDescription(double score) {
    if (score >= 80) {
      return 'Excellente performance ! Continuez sur cette lancée.';
    } else if (score >= 60) {
      return 'Bonne performance. Quelques améliorations possibles.';
    } else if (score >= 40) {
      return 'Performance moyenne. Des efforts sont nécessaires.';
    } else {
      return 'Performance à améliorer. Concentrez-vous sur la ponctualité.';
    }
  }

  IconData _getPointageTypeIcon(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Icons.login;
      case TypePointage.sortie:
        return Icons.logout;
      case TypePointage.pauseDebut:
        return Icons.pause;
      case TypePointage.pauseFin:
        return Icons.play_arrow;
    }
  }

  Color _getPointageTypeColor(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Colors.green;
      case TypePointage.sortie:
        return Colors.red;
      case TypePointage.pauseDebut:
        return Colors.orange;
      case TypePointage.pauseFin:
        return Colors.blue;
    }
  }

  String _getPointageTypeLabel(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return 'Entrée';
      case TypePointage.sortie:
        return 'Sortie';
      case TypePointage.pauseDebut:
        return 'Début pause';
      case TypePointage.pauseFin:
        return 'Fin pause';
    }
  }

  String _getPeriodTitle() {
    switch (_selectedPeriod) {
      case 'Semaine':
        return 'Cette semaine';
      case 'Mois':
        return DateFormat('MMMM yyyy', 'fr_FR').format(_selectedDate);
      case 'Trimestre':
        final quarter = ((_selectedDate.month - 1) ~/ 3) + 1;
        return 'T$quarter ${_selectedDate.year}';
      case 'Année':
        return 'Année ${_selectedDate.year}';
      default:
        return 'Période sélectionnée';
    }
  }

  void _showDatePicker() {
    showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    ).then((date) {
      if (date != null) {
        setState(() {
          _selectedDate = date;
        });
        _loadStatistics();
      }
    });
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: Colors.white, child: _tabBar);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
