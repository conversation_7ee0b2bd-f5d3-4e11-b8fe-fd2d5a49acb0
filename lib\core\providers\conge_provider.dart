import 'package:flutter/foundation.dart';
import '../models/conge.dart';
import '../services/database_service.dart';
import '../services/sync_service.dart';
import 'auth_provider.dart';

class CongeProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final SyncService _syncService = SyncService();
  final AuthProvider _authProvider = AuthProvider();

  // État des congés
  bool _isLoading = false;
  String? _errorMessage;
  List<Conge> _conges = [];
  List<Conge> _congesEnAttente = [];
  List<Conge> _congesApprouves = [];
  List<Conge> _congesRejetes = [];
  
  // Statistiques
  int _joursCongesPris = 0;
  int _joursCongesRestants = 0;
  int _joursCongesTotal = 0;
  
  // État de la synchronisation
  bool _isSyncing = false;

  // Getters
  bool get isLoading => _isLoading;
  bool get isSyncing => _isSyncing;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;
  List<Conge> get conges => _conges;
  List<Conge> get congesEnAttente => _congesEnAttente;
  List<Conge> get congesApprouves => _congesApprouves;
  List<Conge> get congesRejetes => _congesRejetes;
  int get joursCongesPris => _joursCongesPris;
  int get joursCongesRestants => _joursCongesRestants;
  int get joursCongesTotal => _joursCongesTotal;

  /// Initialise le provider
  Future<void> initialize() async {
    await loadConges();
    await loadStatistiques();
  }

  /// Charge tous les congés de l'agent
  Future<void> loadConges() async {
    if (_authProvider.currentAgent == null) return;

    _setLoading(true);
    
    try {
      final conges = await _databaseService.getCongesByAgent(_authProvider.currentAgent!.id);
      
      _conges = conges;
      _filtrerCongesParStatut();
      
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement des congés: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Filtre les congés par statut
  void _filtrerCongesParStatut() {
    _congesEnAttente = _conges.where((c) => c.statut == StatutConge.enAttente).toList();
    _congesApprouves = _conges.where((c) => c.statut == StatutConge.approuve).toList();
    _congesRejetes = _conges.where((c) => c.statut == StatutConge.rejete).toList();
  }

  /// Charge les statistiques de congés
  Future<void> loadStatistiques() async {
    if (_authProvider.currentAgent == null) return;

    try {
      final agent = _authProvider.currentAgent!;
      _joursCongesTotal = agent.joursCongesAnnuels;
      
      // Calculer les jours de congés pris (approuvés) cette année
      final anneeActuelle = DateTime.now().year;
      final congesApprouvesAnnee = _congesApprouves.where((conge) => 
        conge.dateDebut.year == anneeActuelle || conge.dateFin.year == anneeActuelle
      ).toList();
      
      _joursCongesPris = congesApprouvesAnnee.fold(0, (total, conge) => total + conge.nombreJours);
      _joursCongesRestants = _joursCongesTotal - _joursCongesPris;
      
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du calcul des statistiques: ${e.toString()}');
    }
  }

  /// Crée une nouvelle demande de congé
  Future<bool> creerDemandeConge({
    required TypeConge type,
    required DateTime dateDebut,
    required DateTime dateFin,
    required String motif,
  }) async {
    if (_authProvider.currentAgent == null) {
      _setError('Aucun agent connecté');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      // Valider la demande
      final validation = _validerDemandeConge(type, dateDebut, dateFin);
      if (!validation.isValid) {
        _setError(validation.errorMessage!);
        return false;
      }

      // Calculer le nombre de jours
      final nombreJours = _calculerNombreJours(dateDebut, dateFin);
      
      // Créer la demande
      final conge = Conge(
        id: _generateId(),
        agentId: _authProvider.currentAgent!.id,
        type: type,
        dateDebut: dateDebut,
        dateFin: dateFin,
        nombreJours: nombreJours,
        motif: motif,
        statut: StatutConge.enAttente,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Sauvegarder en base
      await _databaseService.insertConge(conge);
      
      // Actualiser les données
      await loadConges();
      await loadStatistiques();
      
      // Tenter une synchronisation
      _syncInBackground();
      
      return true;
    } catch (e) {
      _setError('Erreur lors de la création de la demande: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Modifie une demande de congé (si en attente)
  Future<bool> modifierDemandeConge({
    required String congeId,
    TypeConge? type,
    DateTime? dateDebut,
    DateTime? dateFin,
    String? motif,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final conge = _conges.firstWhere((c) => c.id == congeId);
      
      if (conge.statut != StatutConge.enAttente) {
        _setError('Impossible de modifier une demande qui n\'est pas en attente');
        return false;
      }

      final dateDebutFinal = dateDebut ?? conge.dateDebut;
      final dateFinFinal = dateFin ?? conge.dateFin;
      final typeFinal = type ?? conge.type;
      
      // Valider les modifications
      final validation = _validerDemandeConge(typeFinal, dateDebutFinal, dateFinFinal);
      if (!validation.isValid) {
        _setError(validation.errorMessage!);
        return false;
      }

      // Calculer le nouveau nombre de jours
      final nombreJours = _calculerNombreJours(dateDebutFinal, dateFinFinal);
      
      // Mettre à jour la demande
      final congeModifie = conge.copyWith(
        type: typeFinal,
        dateDebut: dateDebutFinal,
        dateFin: dateFinFinal,
        nombreJours: nombreJours,
        motif: motif ?? conge.motif,
        updatedAt: DateTime.now(),
      );

      await _databaseService.updateConge(congeModifie);
      
      // Actualiser les données
      await loadConges();
      await loadStatistiques();
      
      return true;
    } catch (e) {
      _setError('Erreur lors de la modification: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Annule une demande de congé (si en attente)
  Future<bool> annulerDemandeConge(String congeId) async {
    _setLoading(true);
    _clearError();

    try {
      final conge = _conges.firstWhere((c) => c.id == congeId);
      
      if (conge.statut != StatutConge.enAttente) {
        _setError('Impossible d\'annuler une demande qui n\'est pas en attente');
        return false;
      }

      await _databaseService.deleteConge(congeId);
      
      // Actualiser les données
      await loadConges();
      await loadStatistiques();
      
      return true;
    } catch (e) {
      _setError('Erreur lors de l\'annulation: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Valide une demande de congé
  ValidationResult _validerDemandeConge(TypeConge type, DateTime dateDebut, DateTime dateFin) {
    // Vérifier que la date de fin est après la date de début
    if (dateFin.isBefore(dateDebut)) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'La date de fin doit être postérieure à la date de début',
      );
    }

    // Vérifier que les dates ne sont pas dans le passé
    final maintenant = DateTime.now();
    if (dateDebut.isBefore(DateTime(maintenant.year, maintenant.month, maintenant.day))) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Impossible de demander un congé dans le passé',
      );
    }

    // Calculer le nombre de jours
    final nombreJours = _calculerNombreJours(dateDebut, dateFin);
    
    // Vérifier les limites selon le type de congé
    switch (type) {
      case TypeConge.annuel:
        if (nombreJours > _joursCongesRestants) {
          return ValidationResult(
            isValid: false,
            errorMessage: 'Nombre de jours insuffisant (${_joursCongesRestants} jours restants)',
          );
        }
        break;
      case TypeConge.maladie:
        // Pas de limite pour les congés maladie
        break;
      case TypeConge.maternite:
      case TypeConge.paternite:
        // Vérifier les limites légales si nécessaire
        break;
      case TypeConge.sanssolde:
        // Vérifier les politiques de l'entreprise
        break;
    }

    // Vérifier les chevauchements avec d'autres congés approuvés
    final chevauchement = _verifierChevauchement(dateDebut, dateFin);
    if (chevauchement) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Cette période chevauche avec un congé déjà approuvé',
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Vérifie s'il y a un chevauchement avec des congés existants
  bool _verifierChevauchement(DateTime dateDebut, DateTime dateFin) {
    return _congesApprouves.any((conge) => 
      (dateDebut.isBefore(conge.dateFin) || dateDebut.isAtSameMomentAs(conge.dateFin)) &&
      (dateFin.isAfter(conge.dateDebut) || dateFin.isAtSameMomentAs(conge.dateDebut))
    );
  }

  /// Calcule le nombre de jours entre deux dates (excluant les week-ends)
  int _calculerNombreJours(DateTime dateDebut, DateTime dateFin) {
    int jours = 0;
    DateTime current = dateDebut;
    
    while (current.isBefore(dateFin) || current.isAtSameMomentAs(dateFin)) {
      // Exclure les week-ends (samedi = 6, dimanche = 7)
      if (current.weekday < 6) {
        jours++;
      }
      current = current.add(const Duration(days: 1));
    }
    
    return jours;
  }

  /// Synchronise les congés avec le serveur
  Future<bool> synchronize() async {
    _setSyncing(true);
    _clearError();

    try {
      final result = await _syncService.synchronize();
      
      if (result.success) {
        // Recharger les données après synchronisation
        await loadConges();
        await loadStatistiques();
        return true;
      } else {
        _setError(result.errorMessage ?? 'Erreur de synchronisation');
        return false;
      }
    } catch (e) {
      _setError('Erreur de synchronisation: ${e.toString()}');
      return false;
    } finally {
      _setSyncing(false);
    }
  }

  /// Synchronisation en arrière-plan
  void _syncInBackground() {
    _syncService.synchronize().then((result) {
      if (result.success) {
        // Recharger silencieusement les données
        loadConges();
        loadStatistiques();
      }
    }).catchError((e) {
      debugPrint('Erreur de synchronisation en arrière-plan: $e');
    });
  }

  /// Obtient les congés pour une période donnée
  List<Conge> getCongesPourPeriode(DateTime debut, DateTime fin) {
    return _conges.where((conge) => 
      (conge.dateDebut.isBefore(fin) || conge.dateDebut.isAtSameMomentAs(fin)) &&
      (conge.dateFin.isAfter(debut) || conge.dateFin.isAtSameMomentAs(debut))
    ).toList();
  }

  /// Vérifie si l'agent est en congé à une date donnée
  bool isEnConge(DateTime date) {
    return _congesApprouves.any((conge) => conge.isEnCours(date));
  }

  /// Obtient le congé en cours (si applicable)
  Conge? get congeEnCours {
    final maintenant = DateTime.now();
    try {
      return _congesApprouves.firstWhere((conge) => conge.isEnCours(maintenant));
    } catch (e) {
      return null;
    }
  }

  /// Génère un ID unique
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'conge_${timestamp}_${_authProvider.currentAgent?.id}';
  }

  /// Définit l'état de chargement
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Définit l'état de synchronisation
  void _setSyncing(bool syncing) {
    if (_isSyncing != syncing) {
      _isSyncing = syncing;
      notifyListeners();
    }
  }

  /// Définit un message d'erreur
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Efface le message d'erreur
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// Efface l'erreur manuellement
  void clearError() {
    _clearError();
  }

  /// Actualise toutes les données
  Future<void> refresh() async {
    await loadConges();
    await loadStatistiques();
  }
}

/// Résultat de validation
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  ValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}