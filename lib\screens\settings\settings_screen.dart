import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/services/biometric_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final BiometricService _biometricService = BiometricService();
  bool _biometricEnabled = false;
  bool _notificationsEnabled = true;
  bool _darkModeEnabled = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final agent = authProvider.currentAgent;

    if (agent != null) {
      setState(() {
        _biometricEnabled = agent.biometricEnabled;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paramètres'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Section Sécurité
          _buildSectionHeader('Sécurité'),
          _buildSettingsTile(
            icon: Icons.fingerprint,
            title: 'Authentification biométrique',
            subtitle: 'Utiliser l\'empreinte digitale ou Face ID',
            trailing: Switch(
              value: _biometricEnabled,
              onChanged: _toggleBiometric,
            ),
          ),
          _buildSettingsTile(
            icon: Icons.lock,
            title: 'Changer le mot de passe',
            subtitle: 'Modifier votre mot de passe',
            onTap: _changePassword,
          ),

          const SizedBox(height: 24),

          // Section Notifications
          _buildSectionHeader('Notifications'),
          _buildSettingsTile(
            icon: Icons.notifications,
            title: 'Notifications push',
            subtitle: 'Recevoir des notifications',
            trailing: Switch(
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
            ),
          ),

          const SizedBox(height: 24),

          // Section Apparence
          _buildSectionHeader('Apparence'),
          _buildSettingsTile(
            icon: Icons.dark_mode,
            title: 'Mode sombre',
            subtitle: 'Activer le thème sombre',
            trailing: Switch(
              value: _darkModeEnabled,
              onChanged: (value) {
                setState(() {
                  _darkModeEnabled = value;
                });
              },
            ),
          ),

          const SizedBox(height: 24),

          // Section Compte
          _buildSectionHeader('Compte'),
          _buildSettingsTile(
            icon: Icons.person,
            title: 'Profil',
            subtitle: 'Voir et modifier votre profil',
            onTap: _viewProfile,
          ),
          _buildSettingsTile(
            icon: Icons.logout,
            title: 'Déconnexion',
            subtitle: 'Se déconnecter de l\'application',
            onTap: _logout,
          ),

          const SizedBox(height: 24),

          // Section À propos
          _buildSectionHeader('À propos'),
          _buildSettingsTile(
            icon: Icons.info,
            title: 'Version de l\'application',
            subtitle: '1.0.0',
          ),
          _buildSettingsTile(
            icon: Icons.help,
            title: 'Aide et support',
            subtitle: 'Obtenir de l\'aide',
            onTap: _showHelp,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        leading: Icon(icon, color: Theme.of(context).primaryColor),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing:
            trailing ??
            (onTap != null ? const Icon(Icons.chevron_right) : null),
        onTap: onTap,
      ),
    );
  }

  Future<void> _toggleBiometric(bool value) async {
    if (value) {
      final isAvailable = await _biometricService.canCheckBiometrics();
      if (!isAvailable) {
        _showSnackBar('L\'authentification biométrique n\'est pas disponible');
        return;
      }

      final authResult = await _biometricService.authenticate();
      if (authResult.success) {
        setState(() {
          _biometricEnabled = true;
        });
        _showSnackBar('Authentification biométrique activée');
      }
    } else {
      setState(() {
        _biometricEnabled = false;
      });
      _showSnackBar('Authentification biométrique désactivée');
    }
  }

  void _changePassword() {
    // TODO: Implémenter le changement de mot de passe
    _showSnackBar('Fonctionnalité en cours de développement');
  }

  void _viewProfile() {
    Navigator.pushNamed(context, '/profile');
  }

  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Déconnexion'),
            content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('Déconnexion'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.logout();
      if (mounted) {
        Navigator.pushReplacementNamed(context, '/login');
      }
    }
  }

  void _showHelp() {
    // TODO: Implémenter l'aide
    _showSnackBar('Fonctionnalité en cours de développement');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}
