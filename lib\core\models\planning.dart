import 'package:equatable/equatable.dart';

enum TypePlanning {
  travail,
  conge,
  formation,
  reunion,
  autre,
}

class Planning extends Equatable {
  final String id;
  final String agentId;
  final String? agentNom;
  final DateTime date;
  final DateTime heureDebut;
  final DateTime heureFin;
  final TypePlanning type;
  final String? description;
  final String? lieu;
  final String? equipe;
  final bool isRecurrent;
  final String? recurrencePattern;
  final DateTime? recurrenceEnd;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;

  const Planning({
    required this.id,
    required this.agentId,
    this.agentNom,
    required this.date,
    required this.heureDebut,
    required this.heureFin,
    required this.type,
    this.description,
    this.lieu,
    this.equipe,
    this.isRecurrent = false,
    this.recurrencePattern,
    this.recurrenceEnd,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  factory Planning.fromJson(Map<String, dynamic> json) {
    return Planning(
      id: json['id'] as String,
      agentId: json['agent_id'] as String,
      agentNom: json['agent_nom'] as String?,
      date: DateTime.parse(json['date'] as String),
      heureDebut: DateTime.parse(json['heure_debut'] as String),
      heureFin: DateTime.parse(json['heure_fin'] as String),
      type: TypePlanning.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TypePlanning.autre,
      ),
      description: json['description'] as String?,
      lieu: json['lieu'] as String?,
      equipe: json['equipe'] as String?,
      isRecurrent: json['is_recurrent'] as bool? ?? false,
      recurrencePattern: json['recurrence_pattern'] as String?,
      recurrenceEnd: json['recurrence_end'] != null
          ? DateTime.parse(json['recurrence_end'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agent_id': agentId,
      'agent_nom': agentNom,
      'date': date.toIso8601String(),
      'heure_debut': heureDebut.toIso8601String(),
      'heure_fin': heureFin.toIso8601String(),
      'type': type.name,
      'description': description,
      'lieu': lieu,
      'equipe': equipe,
      'is_recurrent': isRecurrent,
      'recurrence_pattern': recurrencePattern,
      'recurrence_end': recurrenceEnd?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  Planning copyWith({
    String? id,
    String? agentId,
    String? agentNom,
    DateTime? date,
    DateTime? heureDebut,
    DateTime? heureFin,
    TypePlanning? type,
    String? description,
    String? lieu,
    String? equipe,
    bool? isRecurrent,
    String? recurrencePattern,
    DateTime? recurrenceEnd,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return Planning(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentNom: agentNom ?? this.agentNom,
      date: date ?? this.date,
      heureDebut: heureDebut ?? this.heureDebut,
      heureFin: heureFin ?? this.heureFin,
      type: type ?? this.type,
      description: description ?? this.description,
      lieu: lieu ?? this.lieu,
      equipe: equipe ?? this.equipe,
      isRecurrent: isRecurrent ?? this.isRecurrent,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      recurrenceEnd: recurrenceEnd ?? this.recurrenceEnd,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  Duration get duree {
    return heureFin.difference(heureDebut);
  }

  double get heuresDecimales {
    return duree.inMinutes / 60.0;
  }

  bool get isToday {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  bool get isPast {
    return date.isBefore(DateTime.now());
  }

  bool get isFuture {
    return date.isAfter(DateTime.now());
  }

  bool get isActive {
    final now = DateTime.now();
    final startDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      heureDebut.hour,
      heureDebut.minute,
    );
    final endDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      heureFin.hour,
      heureFin.minute,
    );
    
    return now.isAfter(startDateTime) && now.isBefore(endDateTime);
  }

  @override
  List<Object?> get props => [
        id,
        agentId,
        agentNom,
        date,
        heureDebut,
        heureFin,
        type,
        description,
        lieu,
        equipe,
        isRecurrent,
        recurrencePattern,
        recurrenceEnd,
        createdAt,
        updatedAt,
        createdBy,
      ];

  @override
  String toString() {
    return 'Planning(id: $id, agentId: $agentId, date: $date, type: $type)';
  }
}

class PlanningTemplate extends Equatable {
  final String id;
  final String nom;
  final String? description;
  final TypePlanning type;
  final Duration dureeParDefaut;
  final String? lieuParDefaut;
  final bool isActif;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;

  const PlanningTemplate({
    required this.id,
    required this.nom,
    this.description,
    required this.type,
    required this.dureeParDefaut,
    this.lieuParDefaut,
    this.isActif = true,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  factory PlanningTemplate.fromJson(Map<String, dynamic> json) {
    return PlanningTemplate(
      id: json['id'] as String,
      nom: json['nom'] as String,
      description: json['description'] as String?,
      type: TypePlanning.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TypePlanning.autre,
      ),
      dureeParDefaut: Duration(minutes: json['duree_par_defaut'] as int),
      lieuParDefaut: json['lieu_par_defaut'] as String?,
      isActif: json['is_actif'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nom': nom,
      'description': description,
      'type': type.name,
      'duree_par_defaut': dureeParDefaut.inMinutes,
      'lieu_par_defaut': lieuParDefaut,
      'is_actif': isActif,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  @override
  List<Object?> get props => [
        id,
        nom,
        description,
        type,
        dureeParDefaut,
        lieuParDefaut,
        isActif,
        createdAt,
        updatedAt,
        createdBy,
      ];

  @override
  String toString() {
    return 'PlanningTemplate(id: $id, nom: $nom, type: $type)';
  }
}