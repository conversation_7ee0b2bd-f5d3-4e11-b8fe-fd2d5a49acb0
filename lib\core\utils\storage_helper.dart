import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Interface pour les opérations de stockage
abstract class IStorageHelper {
  Future<String?> getToken();
  Future<void> saveToken(String token);
  Future<void> clearToken();
  Future<String?> getString(String key);
  Future<void> setString(String key, String value);
  Future<bool?> getBool(String key);
  Future<void> setBool(String key, bool value);
  Future<int?> getInt(String key);
  Future<void> setInt(String key, int value);
  Future<void> remove(String key);
  Future<void> clear();
}

/// Implémentation du stockage utilisant SharedPreferences
class StorageHelper implements IStorageHelper {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'current_user';
  static const String _settingsKey = 'app_settings';
  
  static StorageHelper? _instance;
  static SharedPreferences? _prefs;
  
  StorageHelper._internal();
  
  static Future<StorageHelper> getInstance() async {
    _instance ??= StorageHelper._internal();
    _prefs ??= await SharedPreferences.getInstance();
    return _instance!;
  }
  
  @override
  Future<String?> getToken() async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!.getString(_tokenKey);
  }
  
  @override
  Future<void> saveToken(String token) async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.setString(_tokenKey, token);
  }
  
  @override
  Future<void> clearToken() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.remove(_tokenKey);
  }
  
  @override
  Future<String?> getString(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!.getString(key);
  }
  
  @override
  Future<void> setString(String key, String value) async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.setString(key, value);
  }
  
  @override
  Future<bool?> getBool(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!.getBool(key);
  }
  
  @override
  Future<void> setBool(String key, bool value) async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.setBool(key, value);
  }
  
  @override
  Future<int?> getInt(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    return _prefs!.getInt(key);
  }
  
  @override
  Future<void> setInt(String key, int value) async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.setInt(key, value);
  }
  
  @override
  Future<void> remove(String key) async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.remove(key);
  }
  
  @override
  Future<void> clear() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _prefs!.clear();
  }
  
  // Méthodes utilitaires spécifiques
  Future<void> saveUser(Map<String, dynamic> user) async {
    await setString(_userKey, jsonEncode(user));
  }
  
  Future<Map<String, dynamic>?> getUser() async {
    final userStr = await getString(_userKey);
    return userStr != null ? jsonDecode(userStr) : null;
  }
  
  Future<void> clearUser() async {
    await remove(_userKey);
  }
  
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    await setString(_settingsKey, jsonEncode(settings));
  }
  
  Future<Map<String, dynamic>?> getSettings() async {
    final settingsStr = await getString(_settingsKey);
    return settingsStr != null ? jsonDecode(settingsStr) : null;
  }
  
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }
}