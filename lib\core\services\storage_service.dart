import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static StorageService? _instance;
  static StorageService get instance => _instance ??= StorageService._();
  
  StorageService._();
  
  SharedPreferences? _prefs;
  
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call init() first.');
    }
    return _prefs!;
  }
  
  // Méthodes utilitaires pour le stockage
  Future<bool> setString(String key, String value) async {
    return await prefs.setString(key, value);
  }
  
  String? getString(String key) {
    return prefs.getString(key);
  }
  
  Future<bool> setBool(String key, bool value) async {
    return await prefs.setBool(key, value);
  }
  
  bool? getBool(String key) {
    return prefs.getBool(key);
  }
  
  Future<bool> setInt(String key, int value) async {
    return await prefs.setInt(key, value);
  }
  
  int? getInt(String key) {
    return prefs.getInt(key);
  }
  
  Future<bool> setDouble(String key, double value) async {
    return await prefs.setDouble(key, value);
  }
  
  double? getDouble(String key) {
    return prefs.getDouble(key);
  }
  
  Future<bool> setStringList(String key, List<String> value) async {
    return await prefs.setStringList(key, value);
  }
  
  List<String>? getStringList(String key) {
    return prefs.getStringList(key);
  }
  
  Future<bool> remove(String key) async {
    return await prefs.remove(key);
  }
  
  Future<bool> clear() async {
    return await prefs.clear();
  }
  
  bool containsKey(String key) {
    return prefs.containsKey(key);
  }
  
  Set<String> getKeys() {
    return prefs.getKeys();
  }
}