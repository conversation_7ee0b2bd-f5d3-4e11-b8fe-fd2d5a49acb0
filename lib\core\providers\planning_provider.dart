import 'package:flutter/foundation.dart';
import '../models/planning.dart';
import '../services/planning_service.dart';
import '../services/auth_service.dart';

class PlanningProvider with ChangeNotifier {
  final PlanningService _planningService;
  final AuthService _authService;

  PlanningProvider({
    required PlanningService planningService,
    required AuthService authService,
  }) : _planningService = planningService,
       _authService = authService;

  List<Planning> _userPlannings = [];
  List<Planning> _teamPlannings = [];
  List<PlanningTemplate> _templates = [];
  bool _isLoading = false;
  String? _error;
  DateTime _selectedDate = DateTime.now();
  String _selectedView = 'month';

  // Getters
  List<Planning> get userPlannings => _userPlannings;
  List<Planning> get teamPlannings => _teamPlannings;
  List<PlanningTemplate> get templates => _templates;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime get selectedDate => _selectedDate;
  String get selectedView => _selectedView;

  // Getters pour les plannings filtrés
  List<Planning> get todayPlannings {
    final today = DateTime.now();
    return _userPlannings.where((planning) {
      return planning.date.year == today.year &&
          planning.date.month == today.month &&
          planning.date.day == today.day;
    }).toList();
  }

  List<Planning> get weekPlannings {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return _userPlannings.where((planning) {
      return planning.date.isAfter(
            startOfWeek.subtract(const Duration(days: 1)),
          ) &&
          planning.date.isBefore(endOfWeek.add(const Duration(days: 1)));
    }).toList();
  }

  List<Planning> get monthPlannings {
    final now = DateTime.now();
    return _userPlannings.where((planning) {
      return planning.date.year == now.year && planning.date.month == now.month;
    }).toList();
  }

  Planning? get currentPlanning {
    return _userPlannings.where((planning) => planning.isActive).firstOrNull;
  }

  Planning? get nextPlanning {
    final now = DateTime.now();
    final futurePlannings =
        _userPlannings.where((planning) => planning.date.isAfter(now)).toList()
          ..sort((a, b) => a.date.compareTo(b.date));

    return futurePlannings.isNotEmpty ? futurePlannings.first : null;
  }

  // Méthodes de chargement
  Future<void> loadPlanning() async {
    await _loadUserPlanning();
    if (_authService.currentUser?.role == 'manager') {
      await _loadTeamPlanning();
    }
  }

  Future<void> _loadUserPlanning() async {
    try {
      _setLoading(true);
      _clearError();

      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('Utilisateur non connecté');
      }

      _userPlannings = await _planningService.getUserPlanning(userId);
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement du planning: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadTeamPlanning() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      _teamPlannings = await _planningService.getTeamPlanning(userId);
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement du planning d\'équipe: $e');
    }
  }

  Future<void> loadTemplates() async {
    try {
      _setLoading(true);
      _clearError();

      _templates = await _planningService.getTemplates();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement des modèles: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes de gestion des plannings
  Future<bool> createPlanning(Planning planning) async {
    try {
      _setLoading(true);
      _clearError();

      final newPlanning = await _planningService.createPlanning(planning);
      _userPlannings.add(newPlanning);
      _sortPlannings();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la création du planning: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updatePlanning(Planning planning) async {
    try {
      _setLoading(true);
      _clearError();

      final updatedPlanning = await _planningService.updatePlanning(planning);
      final index = _userPlannings.indexWhere((p) => p.id == planning.id);
      if (index != -1) {
        _userPlannings[index] = updatedPlanning;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Erreur lors de la modification du planning: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deletePlanning(String planningId) async {
    try {
      _setLoading(true);
      _clearError();

      await _planningService.deletePlanning(planningId);
      _userPlannings.removeWhere((p) => p.id == planningId);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la suppression du planning: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> createRecurrentPlanning({
    required Planning basePlanning,
    required String pattern,
    required DateTime endDate,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final plannings = await _planningService.createRecurrentPlanning(
        basePlanning: basePlanning,
        pattern: pattern,
        endDate: endDate,
      );

      _userPlannings.addAll(plannings);
      _sortPlannings();
      notifyListeners();
      return true;
    } catch (e) {
      _setError(
        'Erreur lors de la création du planning récurrent: ${e.toString()}',
      );
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes de gestion des templates
  Future<bool> createTemplate(PlanningTemplate template) async {
    try {
      _setLoading(true);
      _clearError();

      final newTemplate = await _planningService.createTemplate(template);
      _templates.add(newTemplate);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la création du modèle: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateTemplate(PlanningTemplate template) async {
    try {
      _setLoading(true);
      _clearError();

      final updatedTemplate = await _planningService.updateTemplate(template);
      final index = _templates.indexWhere((t) => t.id == template.id);
      if (index != -1) {
        _templates[index] = updatedTemplate;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Erreur lors de la modification du modèle: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteTemplate(String templateId) async {
    try {
      _setLoading(true);
      _clearError();

      await _planningService.deleteTemplate(templateId);
      _templates.removeWhere((t) => t.id == templateId);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la suppression du modèle: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes de filtrage et recherche
  List<Planning> getPlanningsForDate(DateTime date) {
    return _userPlannings.where((planning) {
      return planning.date.year == date.year &&
          planning.date.month == date.month &&
          planning.date.day == date.day;
    }).toList();
  }

  List<Planning> getPlanningsForDateRange(DateTime start, DateTime end) {
    return _userPlannings.where((planning) {
      return planning.date.isAfter(start.subtract(const Duration(days: 1))) &&
          planning.date.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  List<Planning> getPlanningsByType(TypePlanning type) {
    return _userPlannings.where((planning) => planning.type == type).toList();
  }

  List<Planning> searchPlannings(String query) {
    final lowerQuery = query.toLowerCase();
    return _userPlannings.where((planning) {
      return planning.description?.toLowerCase().contains(lowerQuery) == true ||
          planning.lieu?.toLowerCase().contains(lowerQuery) == true ||
          planning.agentNom?.toLowerCase().contains(lowerQuery) == true;
    }).toList();
  }

  // Méthodes de calcul
  double getTotalHoursForPeriod(DateTime start, DateTime end) {
    final plannings = getPlanningsForDateRange(
      start,
      end,
    ).where((p) => p.type == TypePlanning.travail);

    return plannings.fold(
      0.0,
      (sum, planning) => sum + planning.heuresDecimales,
    );
  }

  Map<TypePlanning, int> getPlanningCountByType(DateTime start, DateTime end) {
    final plannings = getPlanningsForDateRange(start, end);
    final counts = <TypePlanning, int>{};

    for (final type in TypePlanning.values) {
      counts[type] = plannings.where((p) => p.type == type).length;
    }

    return counts;
  }

  // Méthodes de navigation
  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  void setSelectedView(String view) {
    _selectedView = view;
    notifyListeners();
  }

  void goToToday() {
    _selectedDate = DateTime.now();
    notifyListeners();
  }

  void goToPreviousMonth() {
    _selectedDate = DateTime(_selectedDate.year, _selectedDate.month - 1);
    notifyListeners();
  }

  void goToNextMonth() {
    _selectedDate = DateTime(_selectedDate.year, _selectedDate.month + 1);
    notifyListeners();
  }

  // Méthodes utilitaires
  void _sortPlannings() {
    _userPlannings.sort((a, b) => a.date.compareTo(b.date));
    _teamPlannings.sort((a, b) => a.date.compareTo(b.date));
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  Future<void> refresh() async {
    await loadPlanning();
  }
}

// Extension pour ajouter firstOrNull si elle n'existe pas
extension ListExtension<T> on List<T> {
  T? get firstOrNull => isEmpty ? null : first;
}
