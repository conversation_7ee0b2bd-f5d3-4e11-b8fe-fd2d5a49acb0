import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/agent.dart';
import '../models/pointage.dart';
import '../models/conge.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'pointage_agents.db');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  Future<void> _onCreate(Database db, int version) async {
    // Table des agents
    await db.execute('''
      CREATE TABLE agents (
        id TEXT PRIMARY KEY,
        matricule TEXT UNIQUE NOT NULL,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT NOT NULL,
        telephone TEXT NOT NULL,
        poste TEXT NOT NULL,
        departement TEXT NOT NULL,
        lieu_rattachement TEXT NOT NULL,
        photo_url TEXT,
        date_embauche TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        biometric_enabled INTEGER DEFAULT 0,
        jours_conges_annuels INTEGER DEFAULT 25,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    // Table des pointages
    await db.execute('''
      CREATE TABLE pointages (
        id TEXT PRIMARY KEY,
        agent_id TEXT NOT NULL,
        type INTEGER NOT NULL,
        date_heure TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        adresse TEXT,
        is_validated INTEGER DEFAULT 0,
        commentaire TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        FOREIGN KEY (agent_id) REFERENCES agents (id)
      )
    ''');

    // Table des congés
    await db.execute('''
      CREATE TABLE conges (
        id TEXT PRIMARY KEY,
        agent_id TEXT NOT NULL,
        type INTEGER NOT NULL,
        date_debut TEXT NOT NULL,
        date_fin TEXT NOT NULL,
        nombre_jours INTEGER NOT NULL,
        motif TEXT NOT NULL,
        statut INTEGER DEFAULT 0,
        commentaire_rh TEXT,
        approuve_par TEXT,
        date_approbation TEXT,
        is_synced INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (agent_id) REFERENCES agents (id)
      )
    ''');

    // Index pour optimiser les requêtes
    await db.execute(
      'CREATE INDEX idx_pointages_agent_id ON pointages(agent_id)',
    );
    await db.execute(
      'CREATE INDEX idx_pointages_date ON pointages(date_heure)',
    );
    await db.execute('CREATE INDEX idx_conges_agent_id ON conges(agent_id)');
  }

  // Méthodes pour les agents
  Future<int> insertAgent(Agent agent) async {
    final db = await database;
    return await db.insert('agents', agent.toJson());
  }

  Future<List<Agent>> getAllAgents() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('agents');
    return List.generate(maps.length, (i) => Agent.fromJson(maps[i]));
  }

  Future<Agent?> getAgentById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'agents',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Agent.fromJson(maps.first);
    }
    return null;
  }

  Future<Agent?> getAgentByMatricule(String matricule) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'agents',
      where: 'matricule = ?',
      whereArgs: [matricule],
    );
    if (maps.isNotEmpty) {
      return Agent.fromJson(maps.first);
    }
    return null;
  }

  Future<int> updateAgent(Agent agent) async {
    final db = await database;
    return await db.update(
      'agents',
      agent.toJson(),
      where: 'id = ?',
      whereArgs: [agent.id],
    );
  }

  Future<int> deleteAgent(String id) async {
    final db = await database;
    return await db.delete('agents', where: 'id = ?', whereArgs: [id]);
  }

  Future<int> insertOrUpdateAgent(Agent agent) async {
    final db = await database;
    final existing = await getAgentById(agent.id);
    if (existing != null) {
      return await updateAgent(agent);
    } else {
      return await insertAgent(agent);
    }
  }

  // Méthodes pour les pointages
  Future<int> insertPointage(Pointage pointage) async {
    final db = await database;
    return await db.insert('pointages', pointage.toJson());
  }

  Future<List<Pointage>> getPointagesByAgent(
    String agentId, {
    DateTime? date,
  }) async {
    final db = await database;
    String whereClause = 'agent_id = ?';
    List<dynamic> whereArgs = [agentId];

    if (date != null) {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      whereClause += ' AND date_heure >= ? AND date_heure < ?';
      whereArgs.addAll([
        startOfDay.toIso8601String(),
        endOfDay.toIso8601String(),
      ]);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'pointages',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'date_heure DESC',
    );
    return List.generate(maps.length, (i) => Pointage.fromJson(maps[i]));
  }

  Future<List<Pointage>> getPointagesNonSyncs() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'pointages',
      where: 'is_synced = ?',
      whereArgs: [0],
    );
    return List.generate(maps.length, (i) => Pointage.fromJson(maps[i]));
  }

  Future<List<Pointage>> getPointagesByDateRange(
    String agentId,
    DateTime dateDebut,
    DateTime dateFin,
  ) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'pointages',
      where: 'agent_id = ? AND date_heure >= ? AND date_heure <= ?',
      whereArgs: [
        agentId,
        dateDebut.toIso8601String(),
        dateFin.toIso8601String(),
      ],
      orderBy: 'date_heure DESC',
    );
    return List.generate(maps.length, (i) => Pointage.fromJson(maps[i]));
  }

  Future<List<Pointage>> getUnsyncedPointages() async {
    return await getPointagesNonSyncs();
  }

  Future<int> updatePointage(Pointage pointage) async {
    final db = await database;
    return await db.update(
      'pointages',
      pointage.toJson(),
      where: 'id = ?',
      whereArgs: [pointage.id],
    );
  }

  // Méthodes pour les congés
  Future<int> insertConge(Conge conge) async {
    final db = await database;
    return await db.insert('conges', conge.toJson());
  }

  Future<List<Conge>> getCongesByAgent(String agentId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'conges',
      where: 'agent_id = ?',
      whereArgs: [agentId],
      orderBy: 'date_debut DESC',
    );
    return List.generate(maps.length, (i) => Conge.fromJson(maps[i]));
  }

  Future<List<Conge>> getAllConges() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'conges',
      orderBy: 'created_at DESC',
    );
    return List.generate(maps.length, (i) => Conge.fromJson(maps[i]));
  }

  Future<int> updateConge(Conge conge) async {
    final db = await database;
    return await db.update(
      'conges',
      conge.toJson(),
      where: 'id = ?',
      whereArgs: [conge.id],
    );
  }

  Future<int> deleteConge(String id) async {
    final db = await database;
    return await db.delete('conges', where: 'id = ?', whereArgs: [id]);
  }

  Future<List<Conge>> getUnsyncedConges() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'conges',
      where: 'is_synced = ?',
      whereArgs: [0],
    );
    return List.generate(maps.length, (i) => Conge.fromJson(maps[i]));
  }

  Future<int> insertOrUpdateConge(Conge conge) async {
    final db = await database;
    final existing = await db.query(
      'conges',
      where: 'id = ?',
      whereArgs: [conge.id],
    );
    if (existing.isNotEmpty) {
      return await updateConge(conge);
    } else {
      return await insertConge(conge);
    }
  }

  // Statistiques
  Future<Map<String, dynamic>> getStatistiquesAgent(
    String agentId,
    DateTime debut,
    DateTime fin,
  ) async {
    final db = await database;

    // Nombre total de pointages
    final pointagesCount = await db.rawQuery(
      'SELECT COUNT(*) as count FROM pointages WHERE agent_id = ? AND date_heure >= ? AND date_heure <= ?',
      [agentId, debut.toIso8601String(), fin.toIso8601String()],
    );

    // Heures travaillées
    final pointages = await getPointagesByAgent(agentId);
    final pointagesPeriode =
        pointages
            .where(
              (p) => p.dateHeure.isAfter(debut) && p.dateHeure.isBefore(fin),
            )
            .toList();

    double heuresTravaillees = 0;
    for (int i = 0; i < pointagesPeriode.length - 1; i += 2) {
      if (i + 1 < pointagesPeriode.length) {
        final entree = pointagesPeriode[i];
        final sortie = pointagesPeriode[i + 1];
        if (entree.type == TypePointage.entree &&
            sortie.type == TypePointage.sortie) {
          heuresTravaillees +=
              sortie.dateHeure.difference(entree.dateHeure).inMinutes / 60.0;
        }
      }
    }

    return {
      'nombrePointages': pointagesCount.first['count'],
      'heuresTravaillees': heuresTravaillees,
    };
  }

  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
