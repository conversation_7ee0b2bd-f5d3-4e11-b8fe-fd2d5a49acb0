import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/conge_provider.dart';
import '../../core/models/conge.dart';
import '../common/loading_widget.dart';

class DemandeCongeForm extends StatefulWidget {
  final Conge? congeToEdit;
  final VoidCallback? onSuccess;

  const DemandeCongeForm({super.key, this.congeToEdit, this.onSuccess});

  @override
  State<DemandeCongeForm> createState() => _DemandeCongeFormState();
}

class _DemandeCongeFormState extends State<DemandeCongeForm> {
  final _formKey = GlobalKey<FormState>();
  final _motifController = TextEditingController();

  TypeConge _selectedType = TypeConge.congeAnnuel;
  DateTime? _dateDebut;
  DateTime? _dateFin;
  bool _isLoading = false;
  int _nombreJours = 0;

  @override
  void initState() {
    super.initState();
    if (widget.congeToEdit != null) {
      _initializeWithExistingConge();
    }
  }

  void _initializeWithExistingConge() {
    final conge = widget.congeToEdit!;
    _selectedType = conge.type;
    _dateDebut = conge.dateDebut;
    _dateFin = conge.dateFin;
    _motifController.text = conge.motif;
    _nombreJours = conge.nombreJours;
  }

  @override
  void dispose() {
    _motifController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.congeToEdit != null
                    ? 'Modifier la demande de congé'
                    : 'Nouvelle demande de congé',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Type de congé
              _buildTypeCongeSelector(),
              const SizedBox(height: 20),

              // Dates
              Row(
                children: [
                  Expanded(child: _buildDateDebutSelector()),
                  const SizedBox(width: 16),
                  Expanded(child: _buildDateFinSelector()),
                ],
              ),
              const SizedBox(height: 16),

              // Nombre de jours calculé
              if (_nombreJours > 0) _buildNombreJoursDisplay(),
              const SizedBox(height: 20),

              // Motif
              _buildMotifField(),
              const SizedBox(height: 24),

              // Solde disponible
              _buildSoldeInfo(),
              const SizedBox(height: 24),

              // Boutons d'action
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeCongeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Type de congé',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<TypeConge>(
              value: _selectedType,
              isExpanded: true,
              items:
                  TypeConge.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Icon(_getTypeIcon(type), size: 20),
                          const SizedBox(width: 12),
                          Text(_getTypeLabel(type)),
                        ],
                      ),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                    _calculateNombreJours();
                  });
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateDebutSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date de début',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDateDebut(),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 20),
                const SizedBox(width: 12),
                Text(
                  _dateDebut != null
                      ? _formatDate(_dateDebut!)
                      : 'Sélectionner',
                  style: TextStyle(
                    color: _dateDebut != null ? Colors.black : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateFinSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date de fin',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: () => _selectDateFin(),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, size: 20),
                const SizedBox(width: 12),
                Text(
                  _dateFin != null ? _formatDate(_dateFin!) : 'Sélectionner',
                  style: TextStyle(
                    color: _dateFin != null ? Colors.black : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNombreJoursDisplay() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.access_time,
            color: Theme.of(context).primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Text(
            'Durée: $_nombreJours jour${_nombreJours > 1 ? 's' : ''}',
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotifField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Motif',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _motifController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Décrivez le motif de votre demande...',
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            contentPadding: const EdgeInsets.all(16),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Le motif est obligatoire';
            }
            if (value.trim().length < 10) {
              return 'Le motif doit contenir au moins 10 caractères';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildSoldeInfo() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[700], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Solde de congés',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Jours disponibles:'),
                  Text(
                    '${congeProvider.joursCongesRestants}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Jours demandés:'),
                  Text(
                    '$_nombreJours',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color:
                          _nombreJours > congeProvider.joursCongesRestants
                              ? Colors.red
                              : Colors.green,
                    ),
                  ),
                ],
              ),
              if (_nombreJours > 0)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Solde après demande:'),
                    Text(
                      '${congeProvider.joursCongesRestants - _nombreJours}',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color:
                            (congeProvider.joursCongesRestants - _nombreJours) <
                                    0
                                ? Colors.red
                                : Colors.green,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Annuler'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading || !_isFormValid() ? null : _submitForm,
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child:
                _isLoading
                    ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      widget.congeToEdit != null ? 'Modifier' : 'Soumettre',
                    ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDateDebut() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateDebut ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null) {
      setState(() {
        _dateDebut = date;
        // Si la date de fin est antérieure à la nouvelle date de début, la réinitialiser
        if (_dateFin != null && _dateFin!.isBefore(date)) {
          _dateFin = null;
          _nombreJours = 0;
        } else {
          _calculateNombreJours();
        }
      });
    }
  }

  Future<void> _selectDateFin() async {
    if (_dateDebut == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Veuillez d\'abord sélectionner la date de début'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final date = await showDatePicker(
      context: context,
      initialDate: _dateFin ?? _dateDebut!.add(const Duration(days: 1)),
      firstDate: _dateDebut!,
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('fr', 'FR'),
    );

    if (date != null) {
      setState(() {
        _dateFin = date;
        _calculateNombreJours();
      });
    }
  }

  void _calculateNombreJours() {
    if (_dateDebut != null && _dateFin != null) {
      final difference = _dateFin!.difference(_dateDebut!).inDays + 1;
      setState(() {
        _nombreJours = difference;
      });
    }
  }

  bool _isFormValid() {
    return _dateDebut != null &&
        _dateFin != null &&
        _nombreJours > 0 &&
        _motifController.text.trim().isNotEmpty;
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;
    if (!_isFormValid()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final congeProvider = Provider.of<CongeProvider>(context, listen: false);

      bool success;
      if (widget.congeToEdit != null) {
        success = await congeProvider.modifierDemandeConge(
          congeId: widget.congeToEdit!.id,
          type: _selectedType,
          dateDebut: _dateDebut!,
          dateFin: _dateFin!,
          motif: _motifController.text.trim(),
        );
      } else {
        success = await congeProvider.creerDemandeConge(
          type: _selectedType,
          dateDebut: _dateDebut!,
          dateFin: _dateFin!,
          motif: _motifController.text.trim(),
        );
      }

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.congeToEdit != null
                    ? 'Demande modifiée avec succès'
                    : 'Demande créée avec succès',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop();
          widget.onSuccess?.call();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                congeProvider.errorMessage ?? 'Une erreur est survenue',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getTypeLabel(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return 'Congé annuel';
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return 'Congé maladie';
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return 'Congé maternité';
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return 'Congé paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé sans traitement';
      case TypeConge.absence:
        return 'Absence';
    }
  }

  IconData _getTypeIcon(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return Icons.beach_access;
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return Icons.local_hospital;
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return Icons.child_care;
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return Icons.family_restroom;
      case TypeConge.congeSansTraitement:
        return Icons.money_off;
      case TypeConge.absence:
        return Icons.event_busy;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
