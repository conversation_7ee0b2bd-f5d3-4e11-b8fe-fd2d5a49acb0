import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/pointage.dart';
import '../models/agent.dart';
import '../models/conge.dart';
import 'database_service.dart';

class SyncService {
  static final SyncService _instance = SyncService._internal();
  factory SyncService() => _instance;
  SyncService._internal();

  final DatabaseService _databaseService = DatabaseService();
  
  // Configuration du serveur (à adapter selon l'environnement)
  static const String _baseUrl = 'https://api.pointage-agents.com';
  static const String _apiVersion = 'v1';
  
  // Endpoints
  static const String _loginEndpoint = '/auth/login';
  static const String _pointagesEndpoint = '/pointages';
  static const String _agentsEndpoint = '/agents';
  static const String _congesEndpoint = '/conges';
  static const String _syncEndpoint = '/sync';

  // Clés pour le stockage local
  static const String _tokenKey = 'auth_token';
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const String _serverConfigKey = 'server_config';

  /// Authentifie l'utilisateur auprès du serveur
  Future<AuthResult> authenticate(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl$_apiVersion$_loginEndpoint'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'username': username,
          'password': password,
          'device_info': await _getDeviceInfo(),
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final token = data['token'];
        final agent = Agent.fromJson(data['agent']);
        
        // Sauvegarder le token
        await _saveToken(token);
        
        // Sauvegarder les informations de l'agent
        await _databaseService.insertOrUpdateAgent(agent);
        
        return AuthResult(
          success: true,
          token: token,
          agent: agent,
        );
      } else {
        final errorData = jsonDecode(response.body);
        return AuthResult(
          success: false,
          errorMessage: errorData['message'] ?? 'Erreur d\'authentification',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        errorMessage: 'Erreur de connexion: ${e.toString()}',
      );
    }
  }

  /// Synchronise les données avec le serveur
  Future<SyncResult> synchronize({bool forceFullSync = false}) async {
    try {
      final token = await _getToken();
      if (token == null) {
        return SyncResult(
          success: false,
          errorMessage: 'Token d\'authentification manquant',
        );
      }

      final lastSync = forceFullSync ? null : await _getLastSyncTimestamp();
      
      // 1. Envoyer les pointages non synchronisés
      final uploadResult = await _uploadPendingData(token);
      if (!uploadResult.success) {
        return uploadResult;
      }

      // 2. Récupérer les mises à jour du serveur
      final downloadResult = await _downloadUpdates(token, lastSync);
      if (!downloadResult.success) {
        return downloadResult;
      }

      // 3. Mettre à jour le timestamp de dernière synchronisation
      await _saveLastSyncTimestamp(DateTime.now());

      return SyncResult(
        success: true,
        uploadedCount: uploadResult.uploadedCount,
        downloadedCount: downloadResult.downloadedCount,
      );
    } catch (e) {
      return SyncResult(
        success: false,
        errorMessage: 'Erreur de synchronisation: ${e.toString()}',
      );
    }
  }

  /// Envoie les données en attente vers le serveur
  Future<SyncResult> _uploadPendingData(String token) async {
    try {
      // Récupérer les pointages non synchronisés
      final pointagesNonSync = await _databaseService.getUnsyncedPointages();
      int uploadedCount = 0;

      for (final pointage in pointagesNonSync) {
        final success = await _uploadPointage(token, pointage);
        if (success) {
          // Marquer comme synchronisé
          final updatedPointage = pointage.copyWith(isSynced: true);
          await _databaseService.updatePointage(updatedPointage);
          uploadedCount++;
        }
      }

      // Envoyer les demandes de congé non synchronisées
      final congesNonSync = await _databaseService.getUnsyncedConges();
      for (final conge in congesNonSync) {
        final success = await _uploadConge(token, conge);
        if (success) {
          uploadedCount++;
        }
      }

      return SyncResult(
        success: true,
        uploadedCount: uploadedCount,
      );
    } catch (e) {
      return SyncResult(
        success: false,
        errorMessage: 'Erreur lors de l\'envoi: ${e.toString()}',
      );
    }
  }

  /// Télécharge les mises à jour depuis le serveur
  Future<SyncResult> _downloadUpdates(String token, DateTime? lastSync) async {
    try {
      final queryParams = <String, String>{};
      if (lastSync != null) {
        queryParams['since'] = lastSync.toIso8601String();
      }

      final uri = Uri.parse('$_baseUrl$_apiVersion$_syncEndpoint')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        int downloadedCount = 0;

        // Traiter les mises à jour des agents
        if (data['agents'] != null) {
          for (final agentData in data['agents']) {
            final agent = Agent.fromJson(agentData);
            await _databaseService.insertOrUpdateAgent(agent);
            downloadedCount++;
          }
        }

        // Traiter les mises à jour des congés
        if (data['conges'] != null) {
          for (final congeData in data['conges']) {
            final conge = Conge.fromJson(congeData);
            await _databaseService.insertOrUpdateConge(conge);
            downloadedCount++;
          }
        }

        // Traiter les configurations
        if (data['config'] != null) {
          await _saveServerConfig(data['config']);
        }

        return SyncResult(
          success: true,
          downloadedCount: downloadedCount,
        );
      } else {
        return SyncResult(
          success: false,
          errorMessage: 'Erreur serveur: ${response.statusCode}',
        );
      }
    } catch (e) {
      return SyncResult(
        success: false,
        errorMessage: 'Erreur lors du téléchargement: ${e.toString()}',
      );
    }
  }

  /// Envoie un pointage vers le serveur
  Future<bool> _uploadPointage(String token, Pointage pointage) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl$_apiVersion$_pointagesEndpoint'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(pointage.toJson()),
      );

      return response.statusCode == 201 || response.statusCode == 200;
    } catch (e) {
      print('Erreur lors de l\'envoi du pointage: $e');
      return false;
    }
  }

  /// Envoie une demande de congé vers le serveur
  Future<bool> _uploadConge(String token, Conge conge) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl$_apiVersion$_congesEndpoint'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(conge.toJson()),
      );

      return response.statusCode == 201 || response.statusCode == 200;
    } catch (e) {
      print('Erreur lors de l\'envoi du congé: $e');
      return false;
    }
  }

  /// Vérifie la connectivité réseau
  Future<bool> hasNetworkConnection() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// Synchronisation automatique en arrière-plan
  Future<void> scheduleAutoSync() async {
    // Cette méthode pourrait être utilisée avec un package comme workmanager
    // pour programmer des synchronisations automatiques
    if (await hasNetworkConnection()) {
      await synchronize();
    }
  }

  /// Récupère les informations de l'appareil
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Sauvegarde le token d'authentification
  Future<void> _saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  /// Récupère le token d'authentification
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// Supprime le token d'authentification
  Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  /// Sauvegarde le timestamp de dernière synchronisation
  Future<void> _saveLastSyncTimestamp(DateTime timestamp) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, timestamp.toIso8601String());
  }

  /// Récupère le timestamp de dernière synchronisation
  Future<DateTime?> _getLastSyncTimestamp() async {
    final prefs = await SharedPreferences.getInstance();
    final timestampStr = prefs.getString(_lastSyncKey);
    return timestampStr != null ? DateTime.parse(timestampStr) : null;
  }

  /// Sauvegarde la configuration serveur
  Future<void> _saveServerConfig(Map<String, dynamic> config) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_serverConfigKey, jsonEncode(config));
  }

  /// Récupère la configuration serveur
  Future<Map<String, dynamic>?> getServerConfig() async {
    final prefs = await SharedPreferences.getInstance();
    final configStr = prefs.getString(_serverConfigKey);
    return configStr != null ? jsonDecode(configStr) : null;
  }

  /// Vérifie si l'utilisateur est connecté
  Future<bool> isAuthenticated() async {
    final token = await _getToken();
    return token != null && token.isNotEmpty;
  }

  /// Déconnecte l'utilisateur
  Future<void> logout() async {
    await clearToken();
    // Optionnel: nettoyer d'autres données locales
  }
}

/// Résultat d'authentification
class AuthResult {
  final bool success;
  final String? token;
  final Agent? agent;
  final String? errorMessage;

  AuthResult({
    required this.success,
    this.token,
    this.agent,
    this.errorMessage,
  });

  @override
  String toString() {
    return 'AuthResult{success: $success, agent: ${agent?.nom}, errorMessage: $errorMessage}';
  }
}

/// Résultat de synchronisation
class SyncResult {
  final bool success;
  final int uploadedCount;
  final int downloadedCount;
  final String? errorMessage;

  SyncResult({
    required this.success,
    this.uploadedCount = 0,
    this.downloadedCount = 0,
    this.errorMessage,
  });

  @override
  String toString() {
    return 'SyncResult{success: $success, uploaded: $uploadedCount, downloaded: $downloadedCount, error: $errorMessage}';
  }
}