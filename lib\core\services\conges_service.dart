import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/conge.dart';

class CongesService {
  final String baseUrl;

  CongesService({required this.baseUrl});

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    // TODO: Ajouter l'authentification
  };

  // Gestion des congés utilisateur
  Future<List<Conge>> getUserConges(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/user/$userId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Conge.fromJson(json)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération des congés: $e');
    }
  }

  Future<List<Conge>> getTeamConges(String managerId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/team/$managerId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Conge.fromJson(json)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des congés d\'équipe: $e',
      );
    }
  }

  Future<Conge> getConge(String congeId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/$congeId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Conge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération du congé: $e');
    }
  }

  Future<Conge> createConge(Conge conge) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/conges'),
        headers: _headers,
        body: json.encode(conge.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body)['data'];
        return Conge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la création du congé: $e');
    }
  }

  Future<Conge> updateConge(Conge conge) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/conges/${conge.id}'),
        headers: _headers,
        body: json.encode(conge.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Conge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la modification du congé: $e');
    }
  }

  Future<void> deleteConge(String congeId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/conges/$congeId'),
        headers: _headers,
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la suppression du congé: $e');
    }
  }

  Future<Conge> cancelConge(String congeId, String motif) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUrl/api/conges/$congeId/cancel'),
        headers: _headers,
        body: json.encode({'motif_annulation': motif}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Conge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de l\'annulation du congé: $e');
    }
  }

  // Gestion des approbations (pour les managers)
  Future<Conge> approveConge(String congeId, String? commentaire) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUrl/api/conges/$congeId/approve'),
        headers: _headers,
        body: json.encode({'commentaire_manager': commentaire}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Conge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de l\'approbation du congé: $e');
    }
  }

  Future<Conge> rejectConge(String congeId, String motifRefus) async {
    try {
      final response = await http.patch(
        Uri.parse('$baseUrl/api/conges/$congeId/reject'),
        headers: _headers,
        body: json.encode({'motif_refus': motifRefus}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return Conge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors du refus du congé: $e');
    }
  }

  // Gestion des soldes
  Future<List<SoldeConge>> getUserSoldes(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/soldes/$userId'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => SoldeConge.fromJson(json)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération des soldes: $e');
    }
  }

  Future<SoldeConge> updateSolde(SoldeConge solde) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/conges/soldes/${solde.id}'),
        headers: _headers,
        body: json.encode(solde.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return SoldeConge.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du solde: $e');
    }
  }

  // Statistiques
  Future<CongeStatistiques> getStatistiques(String userId, int annee) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/statistiques/$userId?annee=$annee'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body)['data'];
        return CongeStatistiques.fromJson(data);
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération des statistiques: $e');
    }
  }

  Future<Map<String, dynamic>> getTeamStatistiques(
    String managerId,
    int annee,
  ) async {
    try {
      final response = await http.get(
        Uri.parse(
          '$baseUrl/api/conges/statistiques/team/$managerId?annee=$annee',
        ),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body)['data'];
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des statistiques d\'équipe: $e',
      );
    }
  }

  // Validation et vérifications
  Future<List<Conge>> checkConflicts(Conge conge) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/conges/check-conflicts'),
        headers: _headers,
        body: json.encode({
          'agent_id': conge.agentId,
          'date_debut': conge.dateDebut.toIso8601String(),
          'date_fin': conge.dateFin.toIso8601String(),
          'exclude_id':
              conge.id, // Pour exclure le congé actuel lors de la modification
        }),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Conge.fromJson(json)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la vérification des conflits: $e');
    }
  }

  Future<bool> validateCongeRequest(Conge conge) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/conges/validate'),
        headers: _headers,
        body: json.encode(conge.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['valid'] ?? false;
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la validation: $e');
    }
  }

  Future<Map<String, dynamic>> getValidationRules() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/validation-rules'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body)['data'];
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération des règles: $e');
    }
  }

  // Recherche et filtrage
  Future<List<Conge>> searchConges({
    String? query,
    StatutConge? statut,
    TypeConge? type,
    DateTime? dateDebut,
    DateTime? dateFin,
    String? agentId,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (query != null && query.isNotEmpty) {
        queryParams['q'] = query;
      }
      if (statut != null) {
        queryParams['statut'] = statut.name;
      }
      if (type != null) {
        queryParams['type'] = type.name;
      }
      if (dateDebut != null) {
        queryParams['date_debut'] = dateDebut.toIso8601String();
      }
      if (dateFin != null) {
        queryParams['date_fin'] = dateFin.toIso8601String();
      }
      if (agentId != null) {
        queryParams['agent_id'] = agentId;
      }

      final uri = Uri.parse(
        '$baseUrl/api/conges/search',
      ).replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Conge.fromJson(json)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la recherche: $e');
    }
  }

  // Calendrier et planning
  Future<List<Conge>> getCongesForPeriod(
    DateTime start,
    DateTime end, {
    String? agentId,
    List<StatutConge>? statuts,
  }) async {
    try {
      final queryParams = <String, String>{
        'date_debut': start.toIso8601String(),
        'date_fin': end.toIso8601String(),
      };

      if (agentId != null) {
        queryParams['agent_id'] = agentId;
      }
      if (statuts != null && statuts.isNotEmpty) {
        queryParams['statuts'] = statuts.map((s) => s.name).join(',');
      }

      final uri = Uri.parse(
        '$baseUrl/api/conges/period',
      ).replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((json) => Conge.fromJson(json)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception(
        'Erreur lors de la récupération des congés pour la période: $e',
      );
    }
  }

  // Export et rapports
  Future<String> exportConges({
    String? agentId,
    DateTime? dateDebut,
    DateTime? dateFin,
    String format = 'pdf', // pdf, excel, csv
  }) async {
    try {
      final queryParams = <String, String>{'format': format};

      if (agentId != null) {
        queryParams['agent_id'] = agentId;
      }
      if (dateDebut != null) {
        queryParams['date_debut'] = dateDebut.toIso8601String();
      }
      if (dateFin != null) {
        queryParams['date_fin'] = dateFin.toIso8601String();
      }

      final uri = Uri.parse(
        '$baseUrl/api/conges/export',
      ).replace(queryParameters: queryParams);

      final response = await http.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['download_url'];
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de l\'export: $e');
    }
  }

  // Notifications
  Future<void> sendNotification(
    String congeId,
    String type,
    String message,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/conges/$congeId/notifications'),
        headers: _headers,
        body: json.encode({'type': type, 'message': message}),
      );

      if (response.statusCode != 200 && response.statusCode != 201) {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de l\'envoi de la notification: $e');
    }
  }

  // Jours fériés et calendrier
  Future<List<DateTime>> getJoursFeries(int annee) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/conges/jours-feries?annee=$annee'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body)['data'];
        return data.map((dateStr) => DateTime.parse(dateStr)).toList();
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors de la récupération des jours fériés: $e');
    }
  }

  Future<int> calculateWorkingDays(
    DateTime start,
    DateTime end, {
    List<DateTime>? joursFeries,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/conges/calculate-working-days'),
        headers: _headers,
        body: json.encode({
          'date_debut': start.toIso8601String(),
          'date_fin': end.toIso8601String(),
          'jours_feries': joursFeries?.map((d) => d.toIso8601String()).toList(),
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['working_days'];
      } else {
        throw Exception('Erreur ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      throw Exception('Erreur lors du calcul des jours ouvrés: $e');
    }
  }
}
