import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../core/providers/pointage_provider.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/models/pointage.dart';
import '../../core/services/pointage_service.dart';
import '../../widgets/common/loading_widget.dart';

class PointageScreen extends StatefulWidget {
  const PointageScreen({super.key});

  @override
  State<PointageScreen> createState() => _PointageScreenState();
}

class _PointageScreenState extends State<PointageScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isProcessing = false;
  String? _currentLocation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadData();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  Future<void> _loadData() async {
    final pointageProvider = Provider.of<PointageProvider>(context, listen: false);
    await pointageProvider.chargerPointagesDuJour();
  }

  Future<void> _effectuerPointage(TypePointage type) async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    // Animation de pression
    await _scaleController.forward();
    await _scaleController.reverse();

    try {
      final pointageProvider = Provider.of<PointageProvider>(context, listen: false);
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      final agent = authProvider.currentAgent;
      if (agent == null) {
        throw Exception('Agent non connecté');
      }

      final result = await pointageProvider.effectuerPointage(
        agentId: agent.id,
        type: type,
      );

      if (mounted) {
        if (result.success) {
          _showSuccessDialog(result);
        } else {
          _showErrorDialog(result.errorMessage ?? 'Erreur lors du pointage');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorDialog('Erreur: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  void _showSuccessDialog(PointageResult result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 28,
            ),
            const SizedBox(width: 8),
            const Text('Pointage réussi'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${_getTypeLabel(result.pointage?.type)}'),
            const SizedBox(height: 8),
            Text('Heure: ${_formatTime(result.pointage?.heurePointage)}'),
            if (result.pointage?.adresse != null) ..[
              const SizedBox(height: 8),
              Text('Lieu: ${result.pointage!.adresse}'),
            ],
            if (result.message != null) ..[
              const SizedBox(height: 8),
              Text(
                result.message!,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(
              Icons.error,
              color: Colors.red,
              size: 28,
            ),
            const SizedBox(width: 8),
            const Text('Erreur'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(TypePointage? type) {
    switch (type) {
      case TypePointage.entree:
        return 'Entrée';
      case TypePointage.sortie:
        return 'Sortie';
      case TypePointage.pauseDebut:
        return 'Début de pause';
      case TypePointage.pauseFin:
        return 'Fin de pause';
      default:
        return 'Inconnu';
    }
  }

  String _formatTime(DateTime? time) {
    if (time == null) return 'Inconnu';
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Pointage'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => context.push('/pointage/historique'),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              _buildStatusCard(),
              const SizedBox(height: 24),
              _buildPointageButtons(),
              const SizedBox(height: 24),
              _buildTodayActivity(),
              const SizedBox(height: 100), // Espace pour la bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        final statusInfo = pointageProvider.getStatusInfo();
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  _getStatusColor(statusInfo['status']).withValues(alpha: 0.1),
                  _getStatusColor(statusInfo['status']).withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Column(
              children: [
                Icon(
                  _getStatusIcon(statusInfo['status']),
                  size: 48,
                  color: _getStatusColor(statusInfo['status']),
                ),
                const SizedBox(height: 12),
                Text(
                  statusInfo['message'] ?? 'Statut inconnu',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _getStatusColor(statusInfo['status']),
                  ),
                  textAlign: TextAlign.center,
                ),
                if (statusInfo['duration'] != null) ..[
                  const SizedBox(height: 8),
                  Text(
                    'Durée: ${statusInfo['duration']}',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                if (statusInfo['nextAction'] != null) ..[
                  const SizedBox(height: 8),
                  Text(
                    statusInfo['nextAction'],
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPointageButtons() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        final allowedTypes = pointageProvider.getTypesPointageAutorises();
        final suggestedType = pointageProvider.getTypePointageSuggere();
        
        return Column(
          children: [
            const Text(
              'Actions disponibles',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: allowedTypes.map((type) {
                final isSuggested = type == suggestedType;
                return _buildPointageButton(
                  type: type,
                  isSuggested: isSuggested,
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPointageButton({
    required TypePointage type,
    bool isSuggested = false,
  }) {
    final color = _getTypeColor(type);
    final icon = _getTypeIcon(type);
    final label = _getTypeLabel(type);
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _isProcessing ? _scaleAnimation.value : 1.0,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: isSuggested ? _pulseAnimation.value : 1.0,
                child: GestureDetector(
                  onTap: _isProcessing ? null : () => _effectuerPointage(type),
                  child: Container(
                    width: 140,
                    height: 140,
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSuggested ? color : color.withValues(alpha: 0.3),
                        width: isSuggested ? 3 : 2,
                      ),
                      boxShadow: isSuggested
                          ? [
                              BoxShadow(
                                color: color.withValues(alpha: 0.3),
                                blurRadius: 10,
                                spreadRadius: 2,
                              ),
                            ]
                          : null,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (_isProcessing)
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: CircularProgressIndicator(
                              color: color,
                              strokeWidth: 3,
                            ),
                          )
                        else
                          Icon(
                            icon,
                            size: 40,
                            color: color,
                          ),
                        const SizedBox(height: 12),
                        Text(
                          label,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: color,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (isSuggested) ..[
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              'Suggéré',
                              style: TextStyle(
                                fontSize: 10,
                                color: color,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildTodayActivity() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        final pointages = pointageProvider.pointagesDuJour;
        
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Activité du jour',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${pointages.length} pointage(s)',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (pointages.isEmpty)
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.event_note,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Aucun pointage aujourd\'hui',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  ...pointages.map((pointage) => _buildActivityItem(pointage)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActivityItem(Pointage pointage) {
    final color = _getTypeColor(pointage.type);
    final icon = _getTypeIcon(pointage.type);
    final label = _getTypeLabel(pointage.type);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                Text(
                  _formatTime(pointage.heurePointage),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                if (pointage.adresse != null)
                  Text(
                    pointage.adresse!,
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 10,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          if (pointage.validationBiometrique)
            Icon(
              Icons.verified,
              color: Colors.green,
              size: 16,
            ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status) {
      case 'present':
        return Icons.work;
      case 'absent':
        return Icons.home;
      case 'pause':
        return Icons.pause_circle;
      case 'conge':
        return Icons.event_available;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'present':
        return Colors.green;
      case 'absent':
        return Colors.grey;
      case 'pause':
        return Colors.orange;
      case 'conge':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Icons.login;
      case TypePointage.sortie:
        return Icons.logout;
      case TypePointage.pauseDebut:
        return Icons.pause;
      case TypePointage.pauseFin:
        return Icons.play_arrow;
    }
  }

  Color _getTypeColor(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Colors.green;
      case TypePointage.sortie:
        return Colors.red;
      case TypePointage.pauseDebut:
        return Colors.orange;
      case TypePointage.pauseFin:
        return Colors.blue;
    }
  }
}