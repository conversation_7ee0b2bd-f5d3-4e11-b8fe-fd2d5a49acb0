import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../core/providers/planning_provider.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/models/planning.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';

class PlanningScreen extends StatefulWidget {
  const PlanningScreen({super.key});

  @override
  State<PlanningScreen> createState() => _PlanningScreenState();
}

class _PlanningScreenState extends State<PlanningScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.month;
  String _selectedView = 'Calendrier';
  final List<String> _views = ['Calendrier', 'Liste', 'Équipes'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPlanning();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPlanning() async {
    final planningProvider = Provider.of<PlanningProvider>(
      context,
      listen: false,
    );
    await planningProvider.loadPlanning();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            _buildAppBar(),
            _buildViewSelector(),
            if (_selectedView == 'Calendrier') _buildTabBar(),
          ];
        },
        body: _buildBody(),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Planning',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.today, color: Colors.white),
          onPressed: () {
            setState(() {
              _focusedDay = DateTime.now();
              _selectedDay = DateTime.now();
            });
          },
        ),
        PopupMenuButton<CalendarFormat>(
          icon: const Icon(Icons.view_module, color: Colors.white),
          onSelected: (format) {
            setState(() {
              _calendarFormat = format;
            });
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: CalendarFormat.month,
                  child: Text('Vue mensuelle'),
                ),
                const PopupMenuItem(
                  value: CalendarFormat.twoWeeks,
                  child: Text('Vue 2 semaines'),
                ),
                const PopupMenuItem(
                  value: CalendarFormat.week,
                  child: Text('Vue semaine'),
                ),
              ],
        ),
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: _loadPlanning,
        ),
      ],
    );
  }

  Widget _buildViewSelector() {
    return SliverToBoxAdapter(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            const Text(
              'Vue:',
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children:
                      _views.map((view) {
                        final isSelected = _selectedView == view;
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Text(view),
                            selected: isSelected,
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedView = view;
                                });
                              }
                            },
                            selectedColor: Theme.of(
                              context,
                            ).primaryColor.withValues(alpha: 0.2),
                            checkmarkColor: Theme.of(context).primaryColor,
                          ),
                        );
                      }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverAppBarDelegate(
        TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
          tabs: const [
            Tab(text: 'Mon planning', icon: Icon(Icons.person)),
            Tab(text: 'Équipe', icon: Icon(Icons.group)),
          ],
        ),
      ),
      pinned: true,
    );
  }

  Widget _buildBody() {
    switch (_selectedView) {
      case 'Calendrier':
        return TabBarView(
          controller: _tabController,
          children: [_buildCalendarView(false), _buildCalendarView(true)],
        );
      case 'Liste':
        return _buildListView();
      case 'Équipes':
        return _buildTeamsView();
      default:
        return _buildCalendarView(false);
    }
  }

  Widget _buildCalendarView(bool isTeamView) {
    return Consumer<PlanningProvider>(
      builder: (context, planningProvider, child) {
        if (planningProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement du planning...'),
          );
        }

        final plannings =
            isTeamView
                ? planningProvider.teamPlannings
                : planningProvider.userPlannings;

        return Column(
          children: [
            // Calendrier
            Container(
              color: Colors.white,
              child: TableCalendar<Planning>(
                firstDay: DateTime.utc(2020, 1, 1),
                lastDay: DateTime.utc(2030, 12, 31),
                focusedDay: _focusedDay,
                selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                calendarFormat: _calendarFormat,
                eventLoader: (day) => _getEventsForDay(plannings, day),
                startingDayOfWeek: StartingDayOfWeek.monday,
                locale: 'fr_FR',
                calendarStyle: CalendarStyle(
                  outsideDaysVisible: false,
                  weekendTextStyle: const TextStyle(color: Colors.red),
                  holidayTextStyle: const TextStyle(color: Colors.red),
                  selectedDecoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                  todayDecoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  markerDecoration: BoxDecoration(
                    color: Colors.orange,
                    shape: BoxShape.circle,
                  ),
                ),
                headerStyle: HeaderStyle(
                  formatButtonVisible: false,
                  titleCentered: true,
                  titleTextStyle: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                onDaySelected: (selectedDay, focusedDay) {
                  setState(() {
                    _selectedDay = selectedDay;
                    _focusedDay = focusedDay;
                  });
                },
                onFormatChanged: (format) {
                  setState(() {
                    _calendarFormat = format;
                  });
                },
                onPageChanged: (focusedDay) {
                  _focusedDay = focusedDay;
                },
              ),
            ),

            // Événements du jour sélectionné
            Expanded(child: _buildDayEvents(plannings)),
          ],
        );
      },
    );
  }

  Widget _buildListView() {
    return Consumer<PlanningProvider>(
      builder: (context, planningProvider, child) {
        if (planningProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement du planning...'),
          );
        }

        final plannings = planningProvider.userPlannings;

        if (plannings.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.calendar_today,
            title: 'Aucun planning',
            subtitle: 'Aucun planning n\'a été défini pour le moment.',
          );
        }

        // Grouper par semaine
        final groupedPlannings = _groupPlanningsByWeek(plannings);

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: groupedPlannings.length,
          itemBuilder: (context, index) {
            final weekStart = groupedPlannings.keys.elementAt(index);
            final weekPlannings = groupedPlannings[weekStart]!;
            return _buildWeekCard(weekStart, weekPlannings);
          },
        );
      },
    );
  }

  Widget _buildTeamsView() {
    return Consumer<PlanningProvider>(
      builder: (context, planningProvider, child) {
        if (planningProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement des équipes...'),
          );
        }

        final teamPlannings = planningProvider.teamPlannings;

        if (teamPlannings.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.group,
            title: 'Aucune équipe',
            subtitle: 'Aucun planning d\'équipe disponible.',
          );
        }

        // Grouper par équipe
        final groupedByTeam = _groupPlanningsByTeam(teamPlannings);

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: groupedByTeam.length,
          itemBuilder: (context, index) {
            final teamName = groupedByTeam.keys.elementAt(index);
            final teamPlannings = groupedByTeam[teamName]!;
            return _buildTeamCard(teamName, teamPlannings);
          },
        );
      },
    );
  }

  Widget _buildDayEvents(List<Planning> plannings) {
    final dayEvents = _getEventsForDay(plannings, _selectedDay);

    if (dayEvents.isEmpty) {
      return Container(
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.event_busy, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'Aucun événement',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'pour le ${DateFormat('dd/MM/yyyy').format(_selectedDay)}',
                style: TextStyle(color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Événements du ${DateFormat('EEEE dd MMMM yyyy', 'fr_FR').format(_selectedDay)}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: dayEvents.length,
              itemBuilder: (context, index) {
                return _buildEventCard(dayEvents[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventCard(Planning planning) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        onTap: () => _showPlanningDetails(planning),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  color: _getTypeColor(planning.type),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getTypeLabel(planning.type),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${DateFormat('HH:mm').format(planning.heureDebut)} - ${DateFormat('HH:mm').format(planning.heureFin)}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    if (planning.description?.isNotEmpty == true) ...[
                      const SizedBox(height: 4),
                      Text(
                        planning.description!,
                        style: TextStyle(color: Colors.grey[500], fontSize: 12),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                _getTypeIcon(planning.type),
                color: _getTypeColor(planning.type),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeekCard(DateTime weekStart, List<Planning> plannings) {
    final weekEnd = weekStart.add(const Duration(days: 6));
    final totalHours = _calculateWeekHours(plannings);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        title: Text(
          'Semaine du ${DateFormat('dd/MM').format(weekStart)} au ${DateFormat('dd/MM/yyyy').format(weekEnd)}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          '${totalHours.toStringAsFixed(1)}h programmées • ${plannings.length} créneaux',
          style: TextStyle(color: Colors.grey[600]),
        ),
        children:
            plannings.map((planning) {
              return ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getTypeColor(planning.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getTypeIcon(planning.type),
                    color: _getTypeColor(planning.type),
                    size: 20,
                  ),
                ),
                title: Text(_getTypeLabel(planning.type)),
                subtitle: Text(
                  '${DateFormat('EEEE dd/MM', 'fr_FR').format(planning.date)}\n${DateFormat('HH:mm').format(planning.heureDebut)} - ${DateFormat('HH:mm').format(planning.heureFin)}',
                ),
                trailing: Text(
                  '${_calculatePlanningDuration(planning).toStringAsFixed(1)}h',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                isThreeLine: true,
                onTap: () => _showPlanningDetails(planning),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildTeamCard(String teamName, List<Planning> plannings) {
    final todayPlannings =
        plannings.where((p) => isSameDay(p.date, DateTime.now())).toList();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        title: Text(
          teamName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'Aujourd\'hui: ${todayPlannings.length} membres actifs',
          style: TextStyle(color: Colors.grey[600]),
        ),
        children:
            todayPlannings.map((planning) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: _getTypeColor(
                    planning.type,
                  ).withValues(alpha: 0.1),
                  child: Icon(
                    Icons.person,
                    color: _getTypeColor(planning.type),
                    size: 20,
                  ),
                ),
                title: Text(planning.agentNom ?? 'Agent'),
                subtitle: Text(
                  '${_getTypeLabel(planning.type)}\n${DateFormat('HH:mm').format(planning.heureDebut)} - ${DateFormat('HH:mm').format(planning.heureFin)}',
                ),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getTypeColor(planning.type).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getTypeLabel(planning.type),
                    style: TextStyle(
                      color: _getTypeColor(planning.type),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                isThreeLine: true,
                onTap: () => _showPlanningDetails(planning),
              );
            }).toList(),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.currentUser?.role != 'manager') {
      return null;
    }

    return FloatingActionButton(
      onPressed: _showNewPlanningDialog,
      backgroundColor: Theme.of(context).primaryColor,
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  List<Planning> _getEventsForDay(List<Planning> plannings, DateTime day) {
    return plannings
        .where((planning) => isSameDay(planning.date, day))
        .toList();
  }

  Map<DateTime, List<Planning>> _groupPlanningsByWeek(
    List<Planning> plannings,
  ) {
    final grouped = <DateTime, List<Planning>>{};

    for (final planning in plannings) {
      final weekStart = _getWeekStart(planning.date);
      grouped.putIfAbsent(weekStart, () => []).add(planning);
    }

    return grouped;
  }

  Map<String, List<Planning>> _groupPlanningsByTeam(List<Planning> plannings) {
    final grouped = <String, List<Planning>>{};

    for (final planning in plannings) {
      final teamName = planning.equipe ?? 'Équipe par défaut';
      grouped.putIfAbsent(teamName, () => []).add(planning);
    }

    return grouped;
  }

  DateTime _getWeekStart(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  double _calculateWeekHours(List<Planning> plannings) {
    return plannings.fold(
      0.0,
      (sum, planning) => sum + _calculatePlanningDuration(planning),
    );
  }

  double _calculatePlanningDuration(Planning planning) {
    return planning.heureFin.difference(planning.heureDebut).inMinutes / 60.0;
  }

  IconData _getTypeIcon(TypePlanning type) {
    switch (type) {
      case TypePlanning.travail:
        return Icons.work;
      case TypePlanning.conge:
        return Icons.beach_access;
      case TypePlanning.formation:
        return Icons.school;
      case TypePlanning.reunion:
        return Icons.meeting_room;
      case TypePlanning.autre:
        return Icons.event;
    }
  }

  Color _getTypeColor(TypePlanning type) {
    switch (type) {
      case TypePlanning.travail:
        return Colors.blue;
      case TypePlanning.conge:
        return Colors.orange;
      case TypePlanning.formation:
        return Colors.purple;
      case TypePlanning.reunion:
        return Colors.green;
      case TypePlanning.autre:
        return Colors.grey;
    }
  }

  String _getTypeLabel(TypePlanning type) {
    switch (type) {
      case TypePlanning.travail:
        return 'Travail';
      case TypePlanning.conge:
        return 'Congé';
      case TypePlanning.formation:
        return 'Formation';
      case TypePlanning.reunion:
        return 'Réunion';
      case TypePlanning.autre:
        return 'Autre';
    }
  }

  void _showPlanningDetails(Planning planning) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(_getTypeLabel(planning.type)),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Date: ${DateFormat('EEEE dd MMMM yyyy', 'fr_FR').format(planning.date)}',
                ),
                const SizedBox(height: 8),
                Text(
                  'Horaires: ${DateFormat('HH:mm').format(planning.heureDebut)} - ${DateFormat('HH:mm').format(planning.heureFin)}',
                ),
                const SizedBox(height: 8),
                Text(
                  'Durée: ${_calculatePlanningDuration(planning).toStringAsFixed(1)}h',
                ),
                if (planning.description?.isNotEmpty == true) ...[
                  const SizedBox(height: 8),
                  Text('Description: ${planning.description}'),
                ],
                if (planning.lieu?.isNotEmpty == true) ...[
                  const SizedBox(height: 8),
                  Text('Lieu: ${planning.lieu}'),
                ],
                if (planning.agentNom?.isNotEmpty == true) ...[
                  const SizedBox(height: 8),
                  Text('Agent: ${planning.agentNom}'),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
              if (Provider.of<AuthProvider>(
                    context,
                    listen: false,
                  ).currentAgent?.role ==
                  'manager') ...[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showEditPlanningDialog(planning);
                  },
                  child: const Text('Modifier'),
                ),
              ],
            ],
          ),
    );
  }

  void _showNewPlanningDialog() {
    // TODO: Implémenter la création de nouveau planning
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Fonctionnalité de création de planning en cours de développement',
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showEditPlanningDialog(Planning planning) {
    // TODO: Implémenter la modification de planning
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Fonctionnalité de modification de planning en cours de développement',
        ),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(color: Colors.white, child: _tabBar);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
