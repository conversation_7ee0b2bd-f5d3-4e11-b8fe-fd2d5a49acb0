import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'core/providers/auth_provider.dart';
import 'core/providers/pointage_provider.dart';
import 'core/providers/conge_provider.dart';
import 'core/router/app_router.dart';
import 'core/theme/app_theme.dart';
import 'core/services/notification_service.dart';
import 'core/services/storage_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configuration de l'orientation
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Configuration de la barre de statut
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  // Initialisation des services
  await _initializeServices();
  
  runApp(const PointageApp());
}

Future<void> _initializeServices() async {
  try {
    // Initialisation du service de stockage
    await StorageService.instance.init();
    
    // Initialisation du service de notifications
    await NotificationService.instance.init();
    
    print('Services initialisés avec succès');
  } catch (e) {
    print('Erreur lors de l\'initialisation des services: $e');
  }
}

class PointageApp extends StatelessWidget {
  const PointageApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => AuthProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => PointageProvider(),
        ),
        ChangeNotifierProvider(
          create: (context) => CongeProvider(),
        ),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp.router(
            title: 'Pointage des Agents',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            routerConfig: AppRouter.router,
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaleFactor: 1.0, // Empêche le scaling du texte
                ),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}

// Classe pour gérer les erreurs globales
class GlobalErrorHandler {
  static void handleError(dynamic error, StackTrace stackTrace) {
    print('Erreur globale: $error');
    print('Stack trace: $stackTrace');
    
    // TODO: Envoyer l'erreur à un service de monitoring
    // comme Crashlytics ou Sentry
  }
}

// Widget pour afficher les erreurs de manière élégante
class ErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  
  const ErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                'Une erreur est survenue',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              if (onRetry != null) ...[
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Réessayer'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

// Widget de chargement global
class GlobalLoadingWidget extends StatelessWidget {
  final String? message;
  
  const GlobalLoadingWidget({
    super.key,
    this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// Configuration des constantes globales
class AppConstants {
  // URLs de l'API
  static const String baseUrl = 'https://api.pointage-agents.com';
  static const String apiVersion = 'v1';
  
  // Timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Clés de stockage
  static const String tokenKey = 'auth_token';
  static const String userKey = 'current_user';
  static const String settingsKey = 'app_settings';
  
  // Paramètres de l'application
  static const String appName = 'Pointage des Agents';
  static const String appVersion = '1.0.0';
  static const int buildNumber = 1;
  
  // Paramètres de géolocalisation
  static const double defaultLatitude = 48.8566;
  static const double defaultLongitude = 2.3522;
  static const double pointageRadius = 100.0; // mètres
  
  // Paramètres de notification
  static const String notificationChannelId = 'pointage_notifications';
  static const String notificationChannelName = 'Notifications de pointage';
  static const String notificationChannelDescription = 'Notifications pour les rappels de pointage';
  
  // Formats de date
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  
  // Couleurs personnalisées
  static const Color primaryColor = Color(0xFF1976D2);
  static const Color secondaryColor = Color(0xFF424242);
  static const Color accentColor = Color(0xFF4CAF50);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color successColor = Color(0xFF4CAF50);
  
  // Tailles
  static const double borderRadius = 8.0;
  static const double cardElevation = 2.0;
  static const double buttonHeight = 48.0;
  static const double iconSize = 24.0;
  
  // Animations
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 2);
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int maxUsernameLength = 50;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache
  static const Duration cacheExpiration = Duration(hours: 1);
  static const int maxCacheSize = 100; // MB
}

// Configuration de l'environnement
enum Environment {
  development,
  staging,
  production,
}

class EnvironmentConfig {
  static const Environment currentEnvironment = Environment.development;
  
  static String get baseUrl {
    switch (currentEnvironment) {
      case Environment.development:
        return 'https://dev-api.pointage-agents.com';
      case Environment.staging:
        return 'https://staging-api.pointage-agents.com';
      case Environment.production:
        return 'https://api.pointage-agents.com';
    }
  }
  
  static bool get isDebug {
    return currentEnvironment != Environment.production;
  }
  
  static bool get enableLogging {
    return currentEnvironment != Environment.production;
  }
}
