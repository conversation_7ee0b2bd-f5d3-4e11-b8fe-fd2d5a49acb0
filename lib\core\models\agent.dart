enum StatutAgent {
  actif,
  inactif,
  conge,
  suspendu,
}

class Agent {
  final String id;
  final String matricule;
  final String nom;
  final String prenom;
  final String email;
  final String telephone;
  final String poste;
  final String departement;
  final String lieuRattachement;
  final String role;
  final String? photoUrl;
  final DateTime dateEmbauche;
  final bool isActive;
  final StatutAgent statut;
  final bool biometricEnabled;
  final int joursCongesAnnuels;
  final DateTime createdAt;
  final DateTime updatedAt;

  Agent({
    required this.id,
    required this.matricule,
    required this.nom,
    required this.prenom,
    required this.email,
    required this.telephone,
    required this.poste,
    required this.departement,
    required this.lieuRattachement,
    required this.role,
    this.photoUrl,
    required this.dateEmbauche,
    this.isActive = true,
    this.statut = StatutAgent.actif,
    this.biometricEnabled = false,
    this.joursCongesAnnuels = 25,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Agent.fromJson(Map<String, dynamic> json) {
    return Agent(
      id: json['id'],
      matricule: json['matricule'],
      nom: json['nom'],
      prenom: json['prenom'],
      email: json['email'],
      telephone: json['telephone'],
      poste: json['poste'],
      departement: json['departement'],
      lieuRattachement: json['lieu_rattachement'],
      role: json['role'] ?? 'agent',
      photoUrl: json['photo_url'],
      dateEmbauche: DateTime.parse(json['date_embauche']),
      isActive: json['is_active'] ?? true,
      statut: StatutAgent.values.firstWhere(
        (e) => e.name == json['statut'],
        orElse: () => StatutAgent.actif,
      ),
      biometricEnabled: json['biometric_enabled'] ?? false,
      joursCongesAnnuels: json['jours_conges_annuels'] ?? 25,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'matricule': matricule,
      'nom': nom,
      'prenom': prenom,
      'email': email,
      'telephone': telephone,
      'poste': poste,
      'departement': departement,
      'lieu_rattachement': lieuRattachement,
      'role': role,
      'photo_url': photoUrl,
      'date_embauche': dateEmbauche.toIso8601String(),
      'is_active': isActive,
      'statut': statut.name,
      'biometric_enabled': biometricEnabled,
      'jours_conges_annuels': joursCongesAnnuels,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get nomComplet => '$prenom $nom';

  Agent copyWith({
    String? id,
    String? matricule,
    String? nom,
    String? prenom,
    String? email,
    String? telephone,
    String? poste,
    String? departement,
    String? lieuRattachement,
    String? role,
    String? photoUrl,
    DateTime? dateEmbauche,
    bool? isActive,
    StatutAgent? statut,
    bool? biometricEnabled,
    int? joursCongesAnnuels,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Agent(
      id: id ?? this.id,
      matricule: matricule ?? this.matricule,
      nom: nom ?? this.nom,
      prenom: prenom ?? this.prenom,
      email: email ?? this.email,
      telephone: telephone ?? this.telephone,
      poste: poste ?? this.poste,
      departement: departement ?? this.departement,
      lieuRattachement: lieuRattachement ?? this.lieuRattachement,
      role: role ?? this.role,
      photoUrl: photoUrl ?? this.photoUrl,
      dateEmbauche: dateEmbauche ?? this.dateEmbauche,
      isActive: isActive ?? this.isActive,
      statut: statut ?? this.statut,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      joursCongesAnnuels: joursCongesAnnuels ?? this.joursCongesAnnuels,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}