import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../core/providers/auth_provider.dart';

class MainNavigationScreen extends StatefulWidget {
  final Widget child;

  const MainNavigationScreen({super.key, required this.child});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _selectedIndex = 0;

  // Liste des routes pour la navigation
  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      route: '/home',
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Accueil',
    ),
    NavigationItem(
      route: '/pointage',
      icon: Icons.fingerprint_outlined,
      selectedIcon: Icons.fingerprint,
      label: 'Pointage',
    ),
    NavigationItem(
      route: '/pointage/historique',
      icon: Icons.history_outlined,
      selectedIcon: Icons.history,
      label: 'Historique',
    ),
    NavigationItem(
      route: '/agents',
      icon: Icons.people_outline,
      selectedIcon: Icons.people,
      label: 'Agents',
    ),
    NavigationItem(
      route: '/profile',
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: 'Profil',
    ),
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateSelectedIndex();
  }

  void _updateSelectedIndex() {
    final location =
        GoRouter.of(context).routerDelegate.currentConfiguration.uri.path;

    // Trouver l'index correspondant à la route actuelle
    int index = -1;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        index = i;
        break;
      }
    }

    if (index != -1 && index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
    }
  }

  void _onItemTapped(int index) {
    if (index != _selectedIndex) {
      setState(() {
        _selectedIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Filtrer les éléments de navigation selon le rôle de l'utilisateur
        final filteredItems = _getFilteredNavigationItems(authProvider);

        return Scaffold(
          body: widget.child,
          bottomNavigationBar: _buildBottomNavigationBar(filteredItems),
          floatingActionButton: _buildFloatingActionButton(),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
        );
      },
    );
  }

  List<NavigationItem> _getFilteredNavigationItems(AuthProvider authProvider) {
    // Si l'utilisateur n'est pas un manager, masquer l'onglet "Agents"
    if (!authProvider.isManager) {
      return _navigationItems.where((item) => item.route != '/agents').toList();
    }
    return _navigationItems;
  }

  Widget _buildBottomNavigationBar(List<NavigationItem> items) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex.clamp(0, items.length - 1),
        onTap: (index) {
          // Ajuster l'index si nécessaire pour les éléments filtrés
          final actualIndex = _getActualIndex(index, items);
          _onItemTapped(actualIndex);
        },
        backgroundColor: Colors.white,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey[600],
        selectedFontSize: 12,
        unselectedFontSize: 12,
        elevation: 0,
        items:
            items.map((item) {
              final itemIndex = _navigationItems.indexOf(item);
              final isSelected = itemIndex == _selectedIndex;

              return BottomNavigationBarItem(
                icon: Container(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Icon(
                    isSelected ? item.selectedIcon : item.icon,
                    size: 24,
                  ),
                ),
                label: item.label,
              );
            }).toList(),
      ),
    );
  }

  int _getActualIndex(int filteredIndex, List<NavigationItem> filteredItems) {
    if (filteredIndex >= filteredItems.length) return 0;
    final item = filteredItems[filteredIndex];
    return _navigationItems.indexOf(item);
  }

  Widget? _buildFloatingActionButton() {
    // Afficher le FAB seulement sur certaines pages
    final currentRoute =
        GoRouter.of(context).routerDelegate.currentConfiguration.uri.path;

    if (currentRoute == '/pointage') {
      return FloatingActionButton(
        onPressed: () {
          // Action de pointage rapide
          _showQuickPointageDialog();
        },
        backgroundColor: Theme.of(context).colorScheme.secondary,
        child: const Icon(Icons.fingerprint, color: Colors.white, size: 28),
      );
    }

    if (currentRoute == '/home') {
      return FloatingActionButton(
        onPressed: () {
          // Navigation vers la page de pointage
          context.go('/pointage');
        },
        backgroundColor: Theme.of(context).primaryColor,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      );
    }

    return null;
  }

  void _showQuickPointageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Pointage rapide'),
            content: const Text(
              'Voulez-vous effectuer un pointage maintenant ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Implémenter le pointage rapide
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Pointage effectué avec succès'),
                      backgroundColor: Colors.green,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
                child: const Text('Pointer'),
              ),
            ],
          ),
    );
  }
}

// Classe pour représenter un élément de navigation
class NavigationItem {
  final String route;
  final IconData icon;
  final IconData selectedIcon;
  final String label;

  const NavigationItem({
    required this.route,
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}
