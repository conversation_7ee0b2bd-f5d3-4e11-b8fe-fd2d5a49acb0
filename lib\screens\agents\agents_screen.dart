import 'package:flutter/material.dart';
import '../../core/models/agent.dart';
import '../../widgets/common/loading_widget.dart';

class AgentsScreen extends StatefulWidget {
  const AgentsScreen({super.key});

  @override
  State<AgentsScreen> createState() => _AgentsScreenState();
}

class _AgentsScreenState extends State<AgentsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();
  String _searchQuery = '';
  StatutAgent? _filtreStatut;
  String? _filtreDepartement;

  // Données simulées - à remplacer par un vrai provider
  List<Agent> _agents = [];
  List<String> _departements = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    // Simulation de chargement des données
    await Future.delayed(const Duration(seconds: 1));

    // Données simulées
    _agents = [
      Agent(
        id: '1',
        matricule: 'EMP001',
        nom: 'Dupont',
        prenom: 'Jean',
        email: '<EMAIL>',
        telephone: '+33 6 12 34 56 78',
        poste: 'Développeur Senior',
        departement: 'IT',
        lieuRattachement: 'Siège social',
        role: 'agent',
        statut: StatutAgent.actif,
        dateEmbauche: DateTime(2020, 3, 15),
        createdAt: DateTime(2020, 3, 15),
        updatedAt: DateTime.now(),
      ),
      Agent(
        id: '2',
        matricule: 'EMP002',
        nom: 'Martin',
        prenom: 'Marie',
        email: '<EMAIL>',
        telephone: '+33 6 98 76 54 32',
        poste: 'Chef de Projet',
        departement: 'IT',
        lieuRattachement: 'Siège social',
        role: 'manager',
        statut: StatutAgent.actif,
        dateEmbauche: DateTime(2018, 9, 1),
        createdAt: DateTime(2018, 9, 1),
        updatedAt: DateTime.now(),
      ),
      Agent(
        id: '3',
        matricule: 'EMP003',
        nom: 'Bernard',
        prenom: 'Pierre',
        email: '<EMAIL>',
        telephone: '+33 6 11 22 33 44',
        poste: 'Comptable',
        departement: 'Finance',
        lieuRattachement: 'Siège social',
        role: 'agent',
        statut: StatutAgent.conge,
        dateEmbauche: DateTime(2019, 6, 10),
        createdAt: DateTime(2019, 6, 10),
        updatedAt: DateTime.now(),
      ),
      Agent(
        id: '4',
        matricule: 'EMP004',
        nom: 'Leroy',
        prenom: 'Sophie',
        email: '<EMAIL>',
        telephone: '+33 6 55 66 77 88',
        poste: 'RH Manager',
        departement: 'RH',
        lieuRattachement: 'Siège social',
        role: 'manager',
        statut: StatutAgent.actif,
        dateEmbauche: DateTime(2017, 2, 20),
        createdAt: DateTime(2017, 2, 20),
        updatedAt: DateTime.now(),
      ),
      Agent(
        id: '5',
        matricule: 'EMP005',
        nom: 'Moreau',
        prenom: 'Luc',
        email: '<EMAIL>',
        telephone: '+33 6 99 88 77 66',
        poste: 'Designer',
        departement: 'Marketing',
        lieuRattachement: 'Siège social',
        role: 'agent',
        statut: StatutAgent.inactif,
        dateEmbauche: DateTime(2021, 1, 5),
        createdAt: DateTime(2021, 1, 5),
        updatedAt: DateTime.now(),
      ),
    ];

    _departements = _agents.map((a) => a.departement).toSet().toList();
    _departements.sort();

    setState(() => _isLoading = false);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Équipe'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Tous', icon: Icon(Icons.people)),
            Tab(text: 'Actifs', icon: Icon(Icons.person)),
            Tab(text: 'Statistiques', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body:
          _isLoading
              ? const Center(child: LoadingWidget())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildAgentsTab(),
                  _buildActifsTab(),
                  _buildStatistiquesTab(),
                ],
              ),
    );
  }

  Widget _buildAgentsTab() {
    return Column(
      children: [
        _buildSearchAndFilters(),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadData,
            child: _buildAgentsList(_getFilteredAgents()),
          ),
        ),
      ],
    );
  }

  Widget _buildActifsTab() {
    final agentsActifs =
        _agents.where((a) => a.statut == StatutAgent.actif).toList();

    return Column(
      children: [
        _buildQuickStats(agentsActifs),
        Expanded(
          child: RefreshIndicator(
            onRefresh: _loadData,
            child: _buildAgentsList(agentsActifs),
          ),
        ),
      ],
    );
  }

  Widget _buildStatistiquesTab() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildStatistiquesGenerales(),
            const SizedBox(height: 16),
            _buildStatistiquesParDepartement(),
            const SizedBox(height: 16),
            _buildStatistiquesParStatut(),
            const SizedBox(height: 100), // Espace pour la bottom nav
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // Barre de recherche
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Rechercher un agent...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchQuery.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchQuery = '');
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          const SizedBox(height: 12),
          // Filtres
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown<StatutAgent>(
                  'Statut',
                  _filtreStatut,
                  StatutAgent.values,
                  (value) => setState(() => _filtreStatut = value),
                  (statut) => _getStatutLabel(statut),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFilterDropdown<String>(
                  'Département',
                  _filtreDepartement,
                  _departements,
                  (value) => setState(() => _filtreDepartement = value),
                  (dept) => dept,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown<T>(
    String label,
    T? value,
    List<T> items,
    Function(T?) onChanged,
    String Function(T) getLabel,
  ) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        DropdownMenuItem<T>(
          value: null,
          child: Text('Tous les ${label.toLowerCase()}s'),
        ),
        ...items.map(
          (item) =>
              DropdownMenuItem<T>(value: item, child: Text(getLabel(item))),
        ),
      ],
      onChanged: onChanged,
    );
  }

  Widget _buildQuickStats(List<Agent> agents) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: _buildQuickStatItem(
              'Présents',
              '${agents.length}',
              Icons.person,
              Colors.green,
            ),
          ),
          Expanded(
            child: _buildQuickStatItem(
              'En congé',
              '${_agents.where((a) => a.statut == StatutAgent.conge).length}',
              Icons.event_available,
              Colors.orange,
            ),
          ),
          Expanded(
            child: _buildQuickStatItem(
              'Inactifs',
              '${_agents.where((a) => a.statut == StatutAgent.inactif).length}',
              Icons.person_off,
              Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildAgentsList(List<Agent> agents) {
    if (agents.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Aucun agent trouvé',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Modifiez vos critères de recherche',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: agents.length,
      itemBuilder: (context, index) {
        final agent = agents[index];
        return _buildAgentCard(agent);
      },
    );
  }

  Widget _buildAgentCard(Agent agent) {
    final statutColor = _getStatutColor(agent.statut);

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showAgentDetails(agent),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 30,
                backgroundColor: statutColor.withValues(alpha: 0.1),
                child: Text(
                  '${agent.prenom[0]}${agent.nom[0]}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: statutColor,
                    fontSize: 18,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Informations
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${agent.prenom} ${agent.nom}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: statutColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _getStatutLabel(agent.statut),
                            style: TextStyle(
                              color: statutColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      agent.poste,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Icon(Icons.business, size: 14, color: Colors.grey[500]),
                        const SizedBox(width: 4),
                        Text(
                          agent.departement,
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(Icons.badge, size: 14, color: Colors.grey[500]),
                        const SizedBox(width: 4),
                        Text(
                          agent.matricule,
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Actions
              PopupMenuButton<String>(
                onSelected: (value) => _handleAgentAction(agent, value),
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'details',
                        child: Row(
                          children: [
                            Icon(Icons.info),
                            SizedBox(width: 8),
                            Text('Détails'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'contact',
                        child: Row(
                          children: [
                            Icon(Icons.phone),
                            SizedBox(width: 8),
                            Text('Contacter'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'pointages',
                        child: Row(
                          children: [
                            Icon(Icons.access_time),
                            SizedBox(width: 8),
                            Text('Pointages'),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatistiquesGenerales() {
    final totalAgents = _agents.length;
    final agentsActifs =
        _agents.where((a) => a.statut == StatutAgent.actif).length;
    final agentsConge =
        _agents.where((a) => a.statut == StatutAgent.conge).length;
    final agentsInactifs =
        _agents.where((a) => a.statut == StatutAgent.inactif).length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Vue d\'ensemble',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total',
                    '$totalAgents',
                    Icons.people,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Actifs',
                    '$agentsActifs',
                    Icons.person,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'En congé',
                    '$agentsConge',
                    Icons.event_available,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Inactifs',
                    '$agentsInactifs',
                    Icons.person_off,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistiquesParDepartement() {
    final departementStats = <String, int>{};
    for (final agent in _agents) {
      departementStats[agent.departement] =
          (departementStats[agent.departement] ?? 0) + 1;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Répartition par département',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...departementStats.entries.map(
              (entry) => _buildDepartementStatItem(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDepartementStatItem(String departement, int count) {
    final total = _agents.length;
    final pourcentage = (count / total * 100).toStringAsFixed(1);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            departement,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Row(
            children: [
              Text(
                '$count agent(s)',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$pourcentage%',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatistiquesParStatut() {
    final statutStats = <StatutAgent, int>{};
    for (final agent in _agents) {
      statutStats[agent.statut] = (statutStats[agent.statut] ?? 0) + 1;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Répartition par statut',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...statutStats.entries.map(
              (entry) => _buildStatutStatItem(entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatutStatItem(StatutAgent statut, int count) {
    final color = _getStatutColor(statut);
    final total = _agents.length;
    final pourcentage = (count / total * 100).toStringAsFixed(1);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _getStatutLabel(statut),
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            '$count',
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          Text(
            '($pourcentage%)',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Agent> _getFilteredAgents() {
    return _agents.where((agent) {
      // Filtre par recherche
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final nomComplet = '${agent.prenom} ${agent.nom}'.toLowerCase();
        final email = agent.email.toLowerCase();
        final poste = agent.poste.toLowerCase();
        final departement = agent.departement.toLowerCase();

        if (!nomComplet.contains(query) &&
            !email.contains(query) &&
            !poste.contains(query) &&
            !departement.contains(query) &&
            !agent.matricule.toLowerCase().contains(query)) {
          return false;
        }
      }

      // Filtre par statut
      if (_filtreStatut != null && agent.statut != _filtreStatut) {
        return false;
      }

      // Filtre par département
      if (_filtreDepartement != null &&
          agent.departement != _filtreDepartement) {
        return false;
      }

      return true;
    }).toList();
  }

  String _getStatutLabel(StatutAgent statut) {
    switch (statut) {
      case StatutAgent.actif:
        return 'Actif';
      case StatutAgent.inactif:
        return 'Inactif';
      case StatutAgent.conge:
        return 'En congé';
      case StatutAgent.suspendu:
        return 'Suspendu';
    }
  }

  Color _getStatutColor(StatutAgent statut) {
    switch (statut) {
      case StatutAgent.actif:
        return Colors.green;
      case StatutAgent.inactif:
        return Colors.red;
      case StatutAgent.conge:
        return Colors.orange;
      case StatutAgent.suspendu:
        return Colors.purple;
    }
  }

  void _handleAgentAction(Agent agent, String action) {
    switch (action) {
      case 'details':
        _showAgentDetails(agent);
        break;
      case 'contact':
        _contactAgent(agent);
        break;
      case 'pointages':
        _showAgentPointages(agent);
        break;
    }
  }

  void _showAgentDetails(Agent agent) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('${agent.prenom} ${agent.nom}'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow('Matricule', agent.matricule),
                  _buildDetailRow('Email', agent.email),
                  _buildDetailRow('Téléphone', agent.telephone),
                  _buildDetailRow('Poste', agent.poste),
                  _buildDetailRow('Département', agent.departement),
                  _buildDetailRow('Statut', _getStatutLabel(agent.statut)),
                  _buildDetailRow(
                    'Lieu de rattachement',
                    agent.lieuRattachement,
                  ),
                  _buildDetailRow(
                    'Date d\'embauche',
                    _formatDate(agent.dateEmbauche),
                  ),
                  _buildDetailRow('Rôle', agent.role),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _contactAgent(Agent agent) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Contacter ${agent.prenom} ${agent.nom}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.email),
                  title: const Text('Email'),
                  subtitle: Text(agent.email),
                  onTap: () {
                    Navigator.of(context).pop();
                    // TODO: Ouvrir l'application email
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Ouverture email vers ${agent.email}'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.phone),
                  title: const Text('Téléphone'),
                  subtitle: Text(agent.telephone),
                  onTap: () {
                    Navigator.of(context).pop();
                    // TODO: Ouvrir l'application téléphone
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Appel vers ${agent.telephone}')),
                    );
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
            ],
          ),
    );
  }

  void _showAgentPointages(Agent agent) {
    // TODO: Implémenter l'affichage des pointages de l'agent
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Pointages de ${agent.prenom} ${agent.nom} - À implémenter',
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
