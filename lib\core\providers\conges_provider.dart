import 'package:flutter/foundation.dart';
import '../models/conge.dart';
import '../services/conges_service.dart';
import '../services/auth_service.dart';

class CongesProvider with ChangeNotifier {
  final CongesService _congesService;
  final AuthService _authService;

  CongesProvider({
    required CongesService congesService,
    required AuthService authService,
  })  : _congesService = congesService,
        _authService = authService;

  List<Conge> _conges = [];
  List<Conge> _teamConges = [];
  List<SoldeConge> _soldes = [];
  CongeStatistiques? _statistiques;
  bool _isLoading = false;
  String? _error;
  StatutConge? _selectedStatut;
  TypeConge? _selectedType;
  DateTime _selectedYear = DateTime.now();

  // Getters
  List<Conge> get conges => _conges;
  List<Conge> get teamConges => _teamConges;
  List<SoldeConge> get soldes => _soldes;
  CongeStatistiques? get statistiques => _statistiques;
  bool get isLoading => _isLoading;
  String? get error => _error;
  StatutConge? get selectedStatut => _selectedStatut;
  TypeConge? get selectedType => _selectedType;
  DateTime get selectedYear => _selectedYear;

  // Getters pour les congés filtrés
  List<Conge> get congesEnAttente {
    return _conges.where((conge) => conge.statut == StatutConge.enAttente).toList();
  }

  List<Conge> get congesApprouves {
    return _conges.where((conge) => conge.statut == StatutConge.approuve).toList();
  }

  List<Conge> get congesRefuses {
    return _conges.where((conge) => conge.statut == StatutConge.refuse).toList();
  }

  List<Conge> get congesActifs {
    return _conges.where((conge) => conge.isActive).toList();
  }

  List<Conge> get congesFuturs {
    return _conges.where((conge) => conge.isFuture).toList();
  }

  List<Conge> get congesFiltered {
    var filtered = _conges.where((conge) {
      bool matchesStatut = _selectedStatut == null || conge.statut == _selectedStatut;
      bool matchesType = _selectedType == null || conge.type == _selectedType;
      bool matchesYear = conge.dateDebut.year == _selectedYear.year;
      return matchesStatut && matchesType && matchesYear;
    }).toList();
    
    // Trier par date de création (plus récent en premier)
    filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return filtered;
  }

  // Getters pour les soldes
  SoldeConge? getSoldeByType(TypeConge type) {
    return _soldes.where((solde) => solde.type == type).firstOrNull;
  }

  double get totalSoldeRestant {
    return _soldes.fold(0.0, (sum, solde) => sum + solde.soldeRestant);
  }

  List<SoldeConge> get soldesExpiring {
    return _soldes.where((solde) => solde.isExpiringSoon).toList();
  }

  // Méthodes de chargement
  Future<void> loadConges() async {
    await _loadUserConges();
    if (_authService.currentUser?.role == 'manager') {
      await _loadTeamConges();
    }
  }

  Future<void> _loadUserConges() async {
    try {
      _setLoading(true);
      _clearError();
      
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('Utilisateur non connecté');
      }

      _conges = await _congesService.getUserConges(userId);
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement des congés: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadTeamConges() async {
    try {
      final userId = _authService.currentUser?.id;
      if (userId == null) return;

      _teamConges = await _congesService.getTeamConges(userId);
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du chargement des congés d\'équipe: $e');
    }
  }

  Future<void> loadSoldes() async {
    try {
      _setLoading(true);
      _clearError();
      
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('Utilisateur non connecté');
      }

      _soldes = await _congesService.getUserSoldes(userId);
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement des soldes: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadStatistiques() async {
    try {
      _setLoading(true);
      _clearError();
      
      final userId = _authService.currentUser?.id;
      if (userId == null) {
        throw Exception('Utilisateur non connecté');
      }

      _statistiques = await _congesService.getStatistiques(
        userId, 
        _selectedYear.year,
      );
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement des statistiques: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes de gestion des congés
  Future<bool> createConge(Conge conge) async {
    try {
      _setLoading(true);
      _clearError();
      
      final newConge = await _congesService.createConge(conge);
      _conges.add(newConge);
      _sortConges();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la création du congé: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateConge(Conge conge) async {
    try {
      _setLoading(true);
      _clearError();
      
      final updatedConge = await _congesService.updateConge(conge);
      final index = _conges.indexWhere((c) => c.id == conge.id);
      if (index != -1) {
        _conges[index] = updatedConge;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Erreur lors de la modification du congé: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> deleteConge(String congeId) async {
    try {
      _setLoading(true);
      _clearError();
      
      await _congesService.deleteConge(congeId);
      _conges.removeWhere((c) => c.id == congeId);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la suppression du congé: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> cancelConge(String congeId, String motif) async {
    try {
      _setLoading(true);
      _clearError();
      
      final cancelledConge = await _congesService.cancelConge(congeId, motif);
      final index = _conges.indexWhere((c) => c.id == congeId);
      if (index != -1) {
        _conges[index] = cancelledConge;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('Erreur lors de l\'annulation du congé: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes pour les managers
  Future<bool> approveConge(String congeId, String? commentaire) async {
    try {
      _setLoading(true);
      _clearError();
      
      final approvedConge = await _congesService.approveConge(congeId, commentaire);
      
      // Mettre à jour dans la liste des congés d'équipe
      final teamIndex = _teamConges.indexWhere((c) => c.id == congeId);
      if (teamIndex != -1) {
        _teamConges[teamIndex] = approvedConge;
      }
      
      // Mettre à jour dans la liste personnelle si c'est le même utilisateur
      final userIndex = _conges.indexWhere((c) => c.id == congeId);
      if (userIndex != -1) {
        _conges[userIndex] = approvedConge;
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de l\'approbation du congé: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> rejectConge(String congeId, String motifRefus) async {
    try {
      _setLoading(true);
      _clearError();
      
      final rejectedConge = await _congesService.rejectConge(congeId, motifRefus);
      
      // Mettre à jour dans la liste des congés d'équipe
      final teamIndex = _teamConges.indexWhere((c) => c.id == congeId);
      if (teamIndex != -1) {
        _teamConges[teamIndex] = rejectedConge;
      }
      
      // Mettre à jour dans la liste personnelle si c'est le même utilisateur
      final userIndex = _conges.indexWhere((c) => c.id == congeId);
      if (userIndex != -1) {
        _conges[userIndex] = rejectedConge;
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors du refus du congé: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes de validation
  Future<bool> validateCongeRequest(Conge conge) async {
    try {
      final conflicts = await _congesService.checkConflicts(conge);
      return conflicts.isEmpty;
    } catch (e) {
      _setError('Erreur lors de la validation: ${e.toString()}');
      return false;
    }
  }

  Future<List<Conge>> getConflicts(Conge conge) async {
    try {
      return await _congesService.checkConflicts(conge);
    } catch (e) {
      _setError('Erreur lors de la vérification des conflits: ${e.toString()}');
      return [];
    }
  }

  // Méthodes de calcul
  int calculateWorkingDays(DateTime start, DateTime end) {
    int workingDays = 0;
    DateTime current = start;
    
    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      // Exclure les weekends (samedi = 6, dimanche = 7)
      if (current.weekday < 6) {
        workingDays++;
      }
      current = current.add(const Duration(days: 1));
    }
    
    return workingDays;
  }

  bool canRequestConge(TypeConge type, int nombreJours) {
    final solde = getSoldeByType(type);
    if (solde == null) return false;
    return solde.soldeRestant >= nombreJours;
  }

  double getProjectedSolde(TypeConge type, int nombreJours) {
    final solde = getSoldeByType(type);
    if (solde == null) return 0;
    return solde.soldeRestant - nombreJours;
  }

  // Méthodes de filtrage
  void setStatutFilter(StatutConge? statut) {
    _selectedStatut = statut;
    notifyListeners();
  }

  void setTypeFilter(TypeConge? type) {
    _selectedType = type;
    notifyListeners();
  }

  void setYearFilter(DateTime year) {
    _selectedYear = year;
    notifyListeners();
  }

  void clearFilters() {
    _selectedStatut = null;
    _selectedType = null;
    _selectedYear = DateTime.now();
    notifyListeners();
  }

  // Méthodes de recherche
  List<Conge> searchConges(String query) {
    final lowerQuery = query.toLowerCase();
    return _conges.where((conge) {
      return conge.motif.toLowerCase().contains(lowerQuery) ||
          conge.commentaire?.toLowerCase().contains(lowerQuery) == true ||
          conge.agentNom?.toLowerCase().contains(lowerQuery) == true;
    }).toList();
  }

  List<Conge> getCongesForPeriod(DateTime start, DateTime end) {
    return _conges.where((conge) {
      return (conge.dateDebut.isBefore(end) || conge.dateDebut.isAtSameMomentAs(end)) &&
          (conge.dateFin.isAfter(start) || conge.dateFin.isAtSameMomentAs(start));
    }).toList();
  }

  List<Conge> getCongesByType(TypeConge type) {
    return _conges.where((conge) => conge.type == type).toList();
  }

  List<Conge> getCongesByStatut(StatutConge statut) {
    return _conges.where((conge) => conge.statut == statut).toList();
  }

  // Méthodes utilitaires
  void _sortConges() {
    _conges.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    _teamConges.sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  void clearError() {
    _clearError();
    notifyListeners();
  }

  Future<void> refresh() async {
    await Future.wait([
      loadConges(),
      loadSoldes(),
      loadStatistiques(),
    ]);
  }

  @override
  void dispose() {
    super.dispose();
  }
}

// Extension pour ajouter firstOrNull si elle n'existe pas
extension ListExtensionConges<T> on List<T> {
  T? get firstOrNull => isEmpty ? null : first;
}