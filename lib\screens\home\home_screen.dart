import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/providers/pointage_provider.dart';
import '../../core/providers/conge_provider.dart';
import '../../core/models/pointage.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final pointageProvider = Provider.of<PointageProvider>(
      context,
      listen: false,
    );
    final congeProvider = Provider.of<CongeProvider>(context, listen: false);

    await Future.wait([
      pointageProvider.loadPointagesDuJour(),
      pointageProvider.loadStatistiques(),
      congeProvider.loadConges(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            SliverPadding(
              padding: const EdgeInsets.all(16.0),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildWelcomeCard(),
                  const SizedBox(height: 16),
                  _buildQuickActions(),
                  const SizedBox(height: 16),
                  _buildStatusCard(),
                  const SizedBox(height: 16),
                  _buildStatisticsCard(),
                  const SizedBox(height: 16),
                  _buildRecentActivity(),
                  const SizedBox(height: 100), // Espace pour la bottom nav
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return SliverAppBar(
          expandedHeight: 120,
          floating: false,
          pinned: true,
          backgroundColor: Theme.of(context).primaryColor,
          flexibleSpace: FlexibleSpaceBar(
            title: Text(
              'Tableau de bord',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).primaryColor,
                    Theme.of(context).primaryColor.withValues(alpha: 0.8),
                  ],
                ),
              ),
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(
                Icons.notifications_outlined,
                color: Colors.white,
              ),
              onPressed: () {
                // TODO: Implémenter les notifications
              },
            ),
            IconButton(
              icon: const Icon(Icons.settings_outlined, color: Colors.white),
              onPressed: () {
                context.push('/settings');
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildWelcomeCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final agent = authProvider.currentAgent;
        if (agent == null) return const SizedBox.shrink();

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  Theme.of(context).primaryColor.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).primaryColor,
                  child: Text(
                    agent.prenom.isNotEmpty
                        ? agent.prenom[0].toUpperCase()
                        : 'A',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bonjour, ${agent.prenom}!',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${agent.nom} - ${agent.poste}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getGreetingMessage(),
                        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getGreetingMessage() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Bonne matinée! Prêt pour une nouvelle journée?';
    } else if (hour < 17) {
      return 'Bon après-midi! Comment se passe votre journée?';
    } else {
      return 'Bonne soirée! Pensez à pointer votre sortie.';
    }
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Actions rapides',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickActionButton(
                  icon: Icons.fingerprint,
                  label: 'Pointer',
                  color: Colors.green,
                  onTap: () => context.push('/pointage'),
                ),
                _buildQuickActionButton(
                  icon: Icons.event_available,
                  label: 'Congés',
                  color: Colors.blue,
                  onTap: () => context.push('/conges'),
                ),
                _buildQuickActionButton(
                  icon: Icons.history,
                  label: 'Historique',
                  color: Colors.orange,
                  onTap: () => context.push('/pointage/historique'),
                ),
                _buildQuickActionButton(
                  icon: Icons.bar_chart,
                  label: 'Stats',
                  color: Colors.purple,
                  onTap: () => context.push('/statistiques'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(icon, color: color, size: 30),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        final statut = pointageProvider.statutActuel;
        final statusInfo = {
          'status': statut,
          'message': statut.label,
          'duration': null, // TODO: Calculate duration if needed
        };

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      _getStatusIcon(statusInfo['status'] as StatutPointage),
                      color: _getStatusColor(
                        statusInfo['status'] as StatutPointage,
                      ),
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Statut actuel',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      statusInfo['status'] as StatutPointage,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getStatusColor(
                        statusInfo['status'] as StatutPointage,
                      ).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    statusInfo['message'] as String? ?? 'Statut inconnu',
                    style: TextStyle(
                      color: _getStatusColor(
                        statusInfo['status'] as StatutPointage,
                      ),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (statusInfo['duration'] != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Durée: ${statusInfo['duration']}',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(StatutPointage status) {
    switch (status) {
      case StatutPointage.present:
        return Icons.work;
      case StatutPointage.absent:
        return Icons.home;
      case StatutPointage.enPause:
        return Icons.pause_circle;
      case StatutPointage.nonPointe:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(StatutPointage status) {
    switch (status) {
      case StatutPointage.present:
        return Colors.green;
      case StatutPointage.absent:
        return Colors.grey;
      case StatutPointage.enPause:
        return Colors.orange;
      case StatutPointage.nonPointe:
        return Colors.grey;
    }
  }

  Widget _buildStatisticsCard() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        final stats = {
          'heuresAujourdhui': pointageProvider.heuresTravailleesAujourdhui,
          'heuresSemaine': pointageProvider.heuresTravailleesSemaine,
          'heuresMois': pointageProvider.heuresTravailleesMois,
        };

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Statistiques du mois',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'Heures aujourd\'hui',
                        '${stats['heuresAujourdhui']?.toStringAsFixed(1) ?? '0.0'}h',
                        Icons.work_outline,
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Heures semaine',
                        '${stats['heuresSemaine']?.toStringAsFixed(1) ?? '0.0'}h',
                        Icons.schedule,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        'Heures mois',
                        '${stats['heuresMois']?.toStringAsFixed(1) ?? '0.0'}h',
                        Icons.access_time,
                        Colors.orange,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        'Total',
                        '${((stats['heuresAujourdhui'] ?? 0.0) + (stats['heuresSemaine'] ?? 0.0) + (stats['heuresMois'] ?? 0.0)).toStringAsFixed(1)}h',
                        Icons.event_busy,
                        Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        final pointages = pointageProvider.pointagesDuJour;

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Activité du jour',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextButton(
                      onPressed: () => context.push('/pointage/historique'),
                      child: const Text('Voir tout'),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                if (pointages.isEmpty)
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.event_note,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Aucun pointage aujourd\'hui',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  ...pointages
                      .take(3)
                      .map((pointage) => _buildActivityItem(pointage)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActivityItem(Pointage pointage) {
    final isEntree = pointage.type == TypePointage.entree;
    final icon = isEntree ? Icons.login : Icons.logout;
    final color = isEntree ? Colors.green : Colors.red;
    final label = isEntree ? 'Entrée' : 'Sortie';

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(fontWeight: FontWeight.w600, color: color),
                ),
                Text(
                  '${pointage.dateHeure.hour.toString().padLeft(2, '0')}:${pointage.dateHeure.minute.toString().padLeft(2, '0')}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
          if (pointage.adresse != null)
            Icon(Icons.location_on, color: Colors.grey[400], size: 16),
        ],
      ),
    );
  }
}
