import 'dart:math';
import 'package:geolocator/geolocator.dart';
import '../models/pointage.dart';
import 'database_service.dart';
import 'biometric_service.dart';
import 'location_service.dart';

class PointageService {
  static final PointageService _instance = PointageService._internal();
  factory PointageService() => _instance;
  PointageService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final BiometricService _biometricService = BiometricService();
  final LocationService _locationService = LocationService();

  // Configuration du périmètre autorisé (en mètres)
  static const double _rayonAutorise = 100.0;

  // Coordonnées du lieu de travail (à configurer selon l'entreprise)
  static const double _latitudeLieuTravail = 14.6928; // Exemple: Dakar
  static const double _longitudeLieuTravail = -17.4467;

  /// Effectue un pointage complet avec authentification biométrique et géolocalisation
  Future<PointageResult> effectuerPointage({
    required String agentId,
    required TypePointage type,
    String? commentaire,
    bool forcerSansGeolocalisation = false,
  }) async {
    try {
      // 1. Vérifier que l'agent existe
      final agent = await _databaseService.getAgentById(agentId);
      if (agent == null) {
        return PointageResult(
          success: false,
          pointage: null,
          errorMessage: 'Agent non trouvé',
          errorType: PointageErrorType.agentNotFound,
        );
      }

      if (!agent.isActive) {
        return PointageResult(
          success: false,
          pointage: null,
          errorMessage: 'Compte agent désactivé',
          errorType: PointageErrorType.agentInactive,
        );
      }

      // 2. Authentification biométrique
      final authResult = await _biometricService.authenticateForAttendance();
      if (!authResult.success) {
        return PointageResult(
          success: false,
          pointage: null,
          errorMessage:
              authResult.errorMessage ??
              'Échec de l\'authentification biométrique',
          errorType: PointageErrorType.biometricAuthFailed,
        );
      }

      // 3. Obtenir la géolocalisation
      Position? position;
      String? adresse;

      if (!forcerSansGeolocalisation) {
        final locationResult = await _locationService.getCurrentPosition();
        if (!locationResult.success) {
          return PointageResult(
            success: false,
            pointage: null,
            errorMessage:
                locationResult.errorMessage ??
                'Impossible d\'obtenir la localisation',
            errorType: PointageErrorType.locationFailed,
          );
        }

        position = locationResult.position!;
        adresse = locationResult.address;

        // 4. Vérifier le périmètre autorisé
        final positionLieuTravail = Position(
          latitude: _latitudeLieuTravail,
          longitude: _longitudeLieuTravail,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );

        if (!_locationService.isWithinAllowedArea(
          position,
          positionLieuTravail,
          _rayonAutorise,
        )) {
          final distance = _locationService.calculateDistance(
            position.latitude,
            position.longitude,
            _latitudeLieuTravail,
            _longitudeLieuTravail,
          );

          return PointageResult(
            success: false,
            pointage: null,
            errorMessage:
                'Vous êtes en dehors du périmètre autorisé (${distance.round()}m du lieu de travail)',
            errorType: PointageErrorType.outsideAllowedArea,
          );
        }
      } else {
        // Mode sans géolocalisation (pour les tests ou cas spéciaux)
        position = Position(
          latitude: 0.0,
          longitude: 0.0,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
        adresse = 'Mode sans géolocalisation';
      }

      // 5. Vérifier les règles de pointage
      final validationResult = await _validerReglePointage(agentId, type);
      if (!validationResult.isValid) {
        return PointageResult(
          success: false,
          pointage: null,
          errorMessage: validationResult.errorMessage,
          errorType: PointageErrorType.businessRuleViolation,
        );
      }

      // 6. Créer et enregistrer le pointage
      final pointage = Pointage(
        id: _generateId(),
        agentId: agentId,
        type: type,
        dateHeure: DateTime.now(),
        latitude: position.latitude,
        longitude: position.longitude,
        adresse: adresse,
        commentaire: commentaire,
        createdAt: DateTime.now(),
      );

      await _databaseService.insertPointage(pointage);

      return PointageResult(
        success: true,
        pointage: pointage,
        errorMessage: null,
        errorType: null,
      );
    } catch (e) {
      return PointageResult(
        success: false,
        pointage: null,
        errorMessage: 'Erreur lors du pointage: ${e.toString()}',
        errorType: PointageErrorType.unknown,
      );
    }
  }

  /// Valide les règles métier pour le pointage
  Future<ValidationResult> _validerReglePointage(
    String agentId,
    TypePointage type,
  ) async {
    try {
      final aujourdhui = DateTime.now();
      final debutJournee = DateTime(
        aujourdhui.year,
        aujourdhui.month,
        aujourdhui.day,
      );

      // Récupérer les pointages du jour
      final pointagesDuJour = await _databaseService.getPointagesByAgent(
        agentId,
        date: debutJournee,
      );

      switch (type) {
        case TypePointage.entree:
          // Vérifier qu'il n'y a pas déjà une entrée sans sortie
          final derniereEntree =
              pointagesDuJour
                  .where((p) => p.type == TypePointage.entree)
                  .isNotEmpty;
          final derniereSortie =
              pointagesDuJour
                  .where((p) => p.type == TypePointage.sortie)
                  .isNotEmpty;

          if (derniereEntree && !derniereSortie) {
            return ValidationResult(
              isValid: false,
              errorMessage:
                  'Vous avez déjà pointé votre entrée. Veuillez pointer votre sortie.',
            );
          }
          break;

        case TypePointage.sortie:
          // Vérifier qu'il y a une entrée avant la sortie
          final entreeAujourdhui =
              pointagesDuJour
                  .where((p) => p.type == TypePointage.entree)
                  .isNotEmpty;

          if (!entreeAujourdhui) {
            return ValidationResult(
              isValid: false,
              errorMessage: 'Vous devez d\'abord pointer votre entrée.',
            );
          }

          // Vérifier qu'il n'y a pas déjà une sortie après la dernière entrée
          final pointagesOrdonnes =
              pointagesDuJour
                ..sort((a, b) => a.dateHeure.compareTo(b.dateHeure));
          if (pointagesOrdonnes.isNotEmpty &&
              pointagesOrdonnes.last.type == TypePointage.sortie) {
            return ValidationResult(
              isValid: false,
              errorMessage: 'Vous avez déjà pointé votre sortie.',
            );
          }
          break;

        case TypePointage.pauseDebut:
          // Vérifier qu'il y a une entrée et pas de pause en cours
          final entreeAujourdhui =
              pointagesDuJour
                  .where((p) => p.type == TypePointage.entree)
                  .isNotEmpty;

          if (!entreeAujourdhui) {
            return ValidationResult(
              isValid: false,
              errorMessage: 'Vous devez d\'abord pointer votre entrée.',
            );
          }

          final pauseEnCours = _isPauseEnCours(pointagesDuJour);
          if (pauseEnCours) {
            return ValidationResult(
              isValid: false,
              errorMessage: 'Une pause est déjà en cours.',
            );
          }
          break;

        case TypePointage.pauseFin:
          // Vérifier qu'il y a une pause en cours
          final pauseEnCours = _isPauseEnCours(pointagesDuJour);
          if (!pauseEnCours) {
            return ValidationResult(
              isValid: false,
              errorMessage: 'Aucune pause en cours à terminer.',
            );
          }
          break;
      }

      return ValidationResult(isValid: true);
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Erreur lors de la validation: ${e.toString()}',
      );
    }
  }

  /// Vérifie si une pause est en cours
  bool _isPauseEnCours(List<Pointage> pointages) {
    final pointagesOrdonnes =
        pointages..sort((a, b) => a.dateHeure.compareTo(b.dateHeure));

    int compteurPause = 0;
    for (final pointage in pointagesOrdonnes) {
      if (pointage.type == TypePointage.pauseDebut) {
        compteurPause++;
      } else if (pointage.type == TypePointage.pauseFin) {
        compteurPause--;
      }
    }

    return compteurPause > 0;
  }

  /// Récupère les pointages d'un agent
  Future<List<Pointage>> getPointagesAgent(
    String agentId, {
    DateTime? date,
  }) async {
    return await _databaseService.getPointagesByAgent(agentId, date: date);
  }

  /// Calcule les heures travaillées pour une journée
  Future<double> calculerHeuresTravaillees(
    String agentId,
    DateTime date,
  ) async {
    final pointages = await getPointagesAgent(agentId, date: date);
    final pointagesOrdonnes =
        pointages..sort((a, b) => a.dateHeure.compareTo(b.dateHeure));

    double heuresTravaillees = 0;
    DateTime? derniereEntree;

    for (final pointage in pointagesOrdonnes) {
      switch (pointage.type) {
        case TypePointage.entree:
          derniereEntree = pointage.dateHeure;
          break;
        case TypePointage.sortie:
          if (derniereEntree != null) {
            heuresTravaillees +=
                pointage.dateHeure.difference(derniereEntree).inMinutes / 60.0;
            derniereEntree = null;
          }
          break;
        case TypePointage.pauseDebut:
          if (derniereEntree != null) {
            heuresTravaillees +=
                pointage.dateHeure.difference(derniereEntree).inMinutes / 60.0;
          }
          break;
        case TypePointage.pauseFin:
          derniereEntree = pointage.dateHeure;
          break;
      }
    }

    return heuresTravaillees;
  }

  /// Génère un ID unique
  String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(9999);
    return '${timestamp}_$random';
  }

  /// Configure les coordonnées du lieu de travail
  static void configurerLieuTravail(double latitude, double longitude) {
    // Cette méthode pourrait être utilisée pour configurer dynamiquement
    // les coordonnées du lieu de travail depuis les paramètres de l'application
  }
}

/// Résultat d'un pointage
class PointageResult {
  final bool success;
  final Pointage? pointage;
  final String? errorMessage;
  final PointageErrorType? errorType;

  PointageResult({
    required this.success,
    this.pointage,
    this.errorMessage,
    this.errorType,
  });

  @override
  String toString() {
    return 'PointageResult{success: $success, pointage: $pointage, errorMessage: $errorMessage}';
  }
}

/// Résultat de validation des règles métier
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  ValidationResult({required this.isValid, this.errorMessage});
}

/// Types d'erreurs de pointage
enum PointageErrorType {
  agentNotFound,
  agentInactive,
  biometricAuthFailed,
  locationFailed,
  outsideAllowedArea,
  businessRuleViolation,
  unknown,
}
