import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../core/providers/conge_provider.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/models/conge.dart';

class DemandeCongeScreen extends StatefulWidget {
  const DemandeCongeScreen({super.key});

  @override
  State<DemandeCongeScreen> createState() => _DemandeCongeScreenState();
}

class _DemandeCongeScreenState extends State<DemandeCongeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _motifController = TextEditingController();
  final _commentaireController = TextEditingController();

  TypeConge _typeConge = TypeConge.congeAnnuel;
  DateTime? _dateDebut;
  DateTime? _dateFin;
  int _nombreJours = 0;
  bool _isLoading = false;
  bool _isCalculating = false;

  @override
  void initState() {
    super.initState();
    _loadStatistiques();
  }

  Future<void> _loadStatistiques() async {
    final congeProvider = Provider.of<CongeProvider>(context, listen: false);
    await congeProvider.loadStatistiques();
  }

  @override
  void dispose() {
    _motifController.dispose();
    _commentaireController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Nouvelle demande'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSoldeCard(),
              const SizedBox(height: 16),
              _buildTypeCongeCard(),
              const SizedBox(height: 16),
              _buildDatesCard(),
              const SizedBox(height: 16),
              _buildMotifCard(),
              const SizedBox(height: 16),
              _buildCommentaireCard(),
              const SizedBox(height: 24),
              _buildSubmitButton(),
              const SizedBox(height: 100), // Espace pour la bottom nav
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSoldeCard() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        // Using individual statistics properties instead of statistiques getter
        final joursCongesPris = congeProvider.joursCongesPris;
        final joursCongesRestants = congeProvider.joursCongesRestants;
        final joursCongesTotal = congeProvider.joursCongesTotal;

        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  Theme.of(context).primaryColor.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Votre solde de congés',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSoldeItem(
                        'Disponibles',
                        '$joursCongesRestants',
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildSoldeItem(
                        'Total',
                        '$joursCongesTotal',
                        Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildSoldeItem(
                        'Utilisés',
                        '$joursCongesPris',
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSoldeItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTypeCongeCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Type de congé',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...TypeConge.values.map((type) => _buildTypeOption(type)),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeOption(TypeConge type) {
    final isSelected = _typeConge == type;
    final color = _getTypeColor(type);
    final icon = _getTypeIcon(type);
    final label = _getTypeLabel(type);
    final description = _getTypeDescription(type);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => setState(() => _typeConge = type),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color:
                isSelected ? color.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? color : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Radio<TypeConge>(
                value: type,
                groupValue: _typeConge,
                onChanged: (value) => setState(() => _typeConge = value!),
                activeColor: color,
              ),
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: isSelected ? color : Colors.black87,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDatesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Période de congé',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    'Date de début',
                    _dateDebut,
                    (date) => _selectDate(true, date),
                    Icons.event,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildDateField(
                    'Date de fin',
                    _dateFin,
                    (date) => _selectDate(false, date),
                    Icons.event_available,
                  ),
                ),
              ],
            ),
            if (_nombreJours > 0) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calculate,
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Durée: $_nombreJours jour(s)',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    if (_isCalculating) ...[
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
            if (_dateDebut != null && _dateFin != null && _nombreJours > 0)
              _buildValidationMessages(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
    String label,
    DateTime? date,
    Function(DateTime) onDateSelected,
    IconData icon,
  ) {
    return InkWell(
      onTap: () => _selectDate(label == 'Date de début', null),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              date != null ? _formatDate(date) : 'Sélectionner',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: date != null ? Colors.black87 : Colors.grey[500],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValidationMessages() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        final joursCongesRestants = congeProvider.joursCongesRestants;
        final messages = <Widget>[];

        // Vérification du solde disponible
        if (_nombreJours > joursCongesRestants) {
          messages.add(
            _buildValidationMessage(
              'Attention: Vous n\'avez que $joursCongesRestants jour(s) disponible(s)',
              Colors.red,
              Icons.warning,
            ),
          );
        }

        // Vérification des weekends
        if (_dateDebut != null && _dateFin != null) {
          final weekendDays = _countWeekendDays(_dateDebut!, _dateFin!);
          if (weekendDays > 0) {
            messages.add(
              _buildValidationMessage(
                'Info: $weekendDays jour(s) de weekend inclus',
                Colors.blue,
                Icons.info,
              ),
            );
          }
        }

        // Vérification des jours fériés (simulation)
        if (_dateDebut != null && _dateFin != null) {
          final holidays = _countHolidays(_dateDebut!, _dateFin!);
          if (holidays > 0) {
            messages.add(
              _buildValidationMessage(
                'Info: $holidays jour(s) férié(s) inclus',
                Colors.green,
                Icons.celebration,
              ),
            );
          }
        }

        if (messages.isEmpty) return const SizedBox.shrink();

        return Column(children: [const SizedBox(height: 12), ...messages]);
      },
    );
  }

  Widget _buildValidationMessage(String message, Color color, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMotifCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Motif de la demande',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _motifController,
              decoration: InputDecoration(
                hintText: 'Décrivez le motif de votre demande...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.edit_note),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le motif est obligatoire';
                }
                if (value.trim().length < 10) {
                  return 'Le motif doit contenir au moins 10 caractères';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentaireCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Commentaire (optionnel)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _commentaireController,
              decoration: InputDecoration(
                hintText: 'Informations complémentaires...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.comment),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _canSubmit() ? _submitDemande : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child:
            _isLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
                : const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.send),
                    SizedBox(width: 8),
                    Text(
                      'Soumettre la demande',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }

  bool _canSubmit() {
    return !_isLoading &&
        _dateDebut != null &&
        _dateFin != null &&
        _nombreJours > 0 &&
        _motifController.text.trim().isNotEmpty;
  }

  Future<void> _selectDate(bool isStartDate, DateTime? initialDate) async {
    final now = DateTime.now();
    final firstDate = isStartDate ? now : (_dateDebut ?? now);
    final lastDate = DateTime(now.year + 2);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? firstDate,
      firstDate: firstDate,
      lastDate: lastDate,
      locale: const Locale('fr', 'FR'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Theme.of(context).primaryColor),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        if (isStartDate) {
          _dateDebut = selectedDate;
          // Reset date de fin si elle est antérieure à la nouvelle date de début
          if (_dateFin != null && _dateFin!.isBefore(selectedDate)) {
            _dateFin = null;
            _nombreJours = 0;
          }
        } else {
          _dateFin = selectedDate;
        }
      });

      _calculateNombreJours();
    }
  }

  Future<void> _calculateNombreJours() async {
    if (_dateDebut == null || _dateFin == null) {
      setState(() => _nombreJours = 0);
      return;
    }

    setState(() => _isCalculating = true);

    try {
      // Calculate working days between dates (excluding weekends)
      int nombreJours = 0;
      DateTime current = _dateDebut!;

      while (current.isBefore(_dateFin!) ||
          current.isAtSameMomentAs(_dateFin!)) {
        // Exclude weekends (Saturday = 6, Sunday = 7)
        if (current.weekday < 6) {
          nombreJours++;
        }
        current = current.add(const Duration(days: 1));
      }

      setState(() {
        _nombreJours = nombreJours;
        _isCalculating = false;
      });
    } catch (e) {
      setState(() {
        _nombreJours = _dateFin!.difference(_dateDebut!).inDays + 1;
        _isCalculating = false;
      });
    }
  }

  int _countWeekendDays(DateTime start, DateTime end) {
    int count = 0;
    DateTime current = start;

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      if (current.weekday == DateTime.saturday ||
          current.weekday == DateTime.sunday) {
        count++;
      }
      current = current.add(const Duration(days: 1));
    }

    return count;
  }

  int _countHolidays(DateTime start, DateTime end) {
    // Simulation de jours fériés (à remplacer par une vraie logique)
    final holidays = [
      DateTime(start.year, 1, 1), // Nouvel An
      DateTime(start.year, 5, 1), // Fête du travail
      DateTime(start.year, 7, 14), // Fête nationale
      DateTime(start.year, 12, 25), // Noël
    ];

    return holidays.where((holiday) {
      return (holiday.isAfter(start) || holiday.isAtSameMomentAs(start)) &&
          (holiday.isBefore(end) || holiday.isAtSameMomentAs(end));
    }).length;
  }

  Future<void> _submitDemande() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final congeProvider = Provider.of<CongeProvider>(context, listen: false);

      final conge = Conge(
        id: '', // Sera généré par le serveur
        agentId: authProvider.currentUser!.id,
        type: _typeConge,
        dateDebut: _dateDebut!,
        dateFin: _dateFin!,
        nombreJours: _nombreJours,
        motif: _motifController.text.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await congeProvider.creerDemandeConge(
        type: conge.type,
        dateDebut: conge.dateDebut,
        dateFin: conge.dateFin,
        motif: conge.motif,
      );

      if (mounted) {
        // Afficher un dialogue de succès
        await showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => AlertDialog(
                icon: Icon(Icons.check_circle, color: Colors.green, size: 48),
                title: const Text('Demande envoyée'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Votre demande de congé a été envoyée avec succès.',
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Récapitulatif',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.grey[700],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text('Type: ${_getTypeLabel(_typeConge)}'),
                          Text(
                            'Période: ${_formatDate(_dateDebut!)} - ${_formatDate(_dateFin!)}',
                          ),
                          Text('Durée: $_nombreJours jour(s)'),
                        ],
                      ),
                    ),
                  ],
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      context.go('/conges');
                    },
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _getTypeLabel(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return 'Congés payés';
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return 'Congé maladie';
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return 'Congé maternité';
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return 'Congé paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé sans traitement';
      case TypeConge.absence:
        return 'Absence';
    }
  }

  String _getTypeDescription(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return 'Congés annuels rémunérés';
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return 'Arrêt maladie avec justificatif';
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return 'Congé de maternité';
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return 'Congé de paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé non rémunéré';
      case TypeConge.absence:
        return 'Absence justifiée';
    }
  }

  IconData _getTypeIcon(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return Icons.beach_access;
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return Icons.local_hospital;
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return Icons.child_care;
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return Icons.family_restroom;
      case TypeConge.congeSansTraitement:
        return Icons.money_off;
      case TypeConge.absence:
        return Icons.event_busy;
    }
  }

  Color _getTypeColor(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return Colors.blue;
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return Colors.red;
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return Colors.pink;
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return Colors.teal;
      case TypeConge.congeSansTraitement:
        return Colors.orange;
      case TypeConge.absence:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
