import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../core/providers/conge_provider.dart';
import '../../core/models/conge.dart';
import '../common/loading_widget.dart';

class CongesCalendar extends StatefulWidget {
  final Function(Conge)? onCongeSelected;
  final bool showLegend;

  const CongesCalendar({
    super.key,
    this.onCongeSelected,
    this.showLegend = true,
  });

  @override
  State<CongesCalendar> createState() => _CongesCalendarState();
}

class _CongesCalendarState extends State<CongesCalendar> {
  late final ValueNotifier<List<Conge>> _selectedEvents;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    _selectedEvents = ValueNotifier(_getEventsForDay(_selectedDay!));
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    super.dispose();
  }

  List<Conge> _getEventsForDay(DateTime day) {
    final congeProvider = context.read<CongeProvider>();
    return congeProvider.conges.where((conge) {
      return _isDateInRange(day, conge.dateDebut, conge.dateFin);
    }).toList();
  }

  bool _isDateInRange(DateTime date, DateTime start, DateTime end) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final startOnly = DateTime(start.year, start.month, start.day);
    final endOnly = DateTime(end.year, end.month, end.day);
    
    return dateOnly.isAtSameMomentAs(startOnly) ||
           dateOnly.isAtSameMomentAs(endOnly) ||
           (dateOnly.isAfter(startOnly) && dateOnly.isBefore(endOnly));
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        if (congeProvider.isLoading) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: LoadingWidget(),
            ),
          );
        }

        return Column(
          children: [
            _buildCalendar(congeProvider),
            if (widget.showLegend) ..[
              const SizedBox(height: 16),
              _buildLegend(),
            ],
            const SizedBox(height: 16),
            _buildSelectedDayEvents(),
          ],
        );
      },
    );
  }

  Widget _buildCalendar(CongeProvider congeProvider) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TableCalendar<Conge>(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _focusedDay,
        calendarFormat: _calendarFormat,
        eventLoader: _getEventsForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,
        locale: 'fr_FR',
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(color: Colors.red[400]),
          holidayTextStyle: TextStyle(color: Colors.red[400]),
          selectedDecoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
            shape: BoxShape.circle,
          ),
          markerDecoration: const BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
          markersMaxCount: 3,
          canMarkersOverflow: true,
        ),
        headerStyle: const HeaderStyle(
          formatButtonVisible: true,
          titleCentered: true,
          formatButtonShowsNext: false,
          formatButtonDecoration: BoxDecoration(
            color: Colors.blue,
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          formatButtonTextStyle: TextStyle(
            color: Colors.white,
          ),
        ),
        onDaySelected: _onDaySelected,
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
            setState(() {
              _calendarFormat = format;
            });
          }
        },
        onPageChanged: (focusedDay) {
          _focusedDay = focusedDay;
        },
        selectedDayPredicate: (day) {
          return isSameDay(_selectedDay, day);
        },
        calendarBuilders: CalendarBuilders(
          markerBuilder: (context, day, events) {
            if (events.isEmpty) return null;
            
            return Positioned(
              bottom: 1,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: events.take(3).map((event) {
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 1),
                    width: 6,
                    height: 6,
                    decoration: BoxDecoration(
                      color: _getStatutColor(event.statut),
                      shape: BoxShape.circle,
                    ),
                  );
                }).toList(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLegend() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Légende',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: [
              _buildLegendItem('En attente', Colors.orange),
              _buildLegendItem('Approuvé', Colors.green),
              _buildLegendItem('Refusé', Colors.red),
              _buildLegendItem('Annulé', Colors.grey),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildSelectedDayEvents() {
    return ValueListenableBuilder<List<Conge>>(
      valueListenable: _selectedEvents,
      builder: (context, value, _) {
        if (value.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Aucun congé pour le ${_formatDate(_selectedDay!)}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Congés du ${_formatDate(_selectedDay!)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Divider(height: 1),
              ...value.map((conge) => _buildEventTile(conge)),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEventTile(Conge conge) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: _getTypeColor(conge.type).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          _getTypeIcon(conge.type),
          color: _getTypeColor(conge.type),
          size: 20,
        ),
      ),
      title: Text(
        _getTypeLabel(conge.type),
        style: const TextStyle(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${_formatDate(conge.dateDebut)} - ${_formatDate(conge.dateFin)}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          if (conge.motif.isNotEmpty)
            Text(
              conge.motif,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
      trailing: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getStatutColor(conge.statut).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getStatutColor(conge.statut).withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          _getStatutLabel(conge.statut),
          style: TextStyle(
            color: _getStatutColor(conge.statut),
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      onTap: () => widget.onCongeSelected?.call(conge),
    );
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
      });
      _selectedEvents.value = _getEventsForDay(selectedDay);
    }
  }

  String _getTypeLabel(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return 'Congé annuel';
      case TypeConge.congeMaladie:
        return 'Congé maladie';
      case TypeConge.congeMaternite:
        return 'Congé maternité';
      case TypeConge.congePaternite:
        return 'Congé paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé sans traitement';
      case TypeConge.absence:
        return 'Absence';
    }
  }

  String _getStatutLabel(StatutConge statut) {
    switch (statut) {
      case StatutConge.enAttente:
        return 'En attente';
      case StatutConge.approuve:
        return 'Approuvé';
      case StatutConge.refuse:
        return 'Refusé';
      case StatutConge.annule:
        return 'Annulé';
    }
  }

  IconData _getTypeIcon(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return Icons.beach_access;
      case TypeConge.congeMaladie:
        return Icons.local_hospital;
      case TypeConge.congeMaternite:
        return Icons.child_care;
      case TypeConge.congePaternite:
        return Icons.family_restroom;
      case TypeConge.congeSansTraitement:
        return Icons.money_off;
      case TypeConge.absence:
        return Icons.event_busy;
    }
  }

  Color _getTypeColor(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return Colors.blue;
      case TypeConge.congeMaladie:
        return Colors.red;
      case TypeConge.congeMaternite:
        return Colors.pink;
      case TypeConge.congePaternite:
        return Colors.indigo;
      case TypeConge.congeSansTraitement:
        return Colors.orange;
      case TypeConge.absence:
        return Colors.grey;
    }
  }

  Color _getStatutColor(StatutConge statut) {
    switch (statut) {
      case StatutConge.enAttente:
        return Colors.orange;
      case StatutConge.approuve:
        return Colors.green;
      case StatutConge.refuse:
        return Colors.red;
      case StatutConge.annule:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}