import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/planning.dart';
import '../config/api_config.dart';
import '../utils/storage_helper.dart';

class PlanningService {
  final http.Client _client;
  final IStorageHelper _storageHelper;

  PlanningService({
    http.Client? client,
    required IStorageHelper storageHelper,
  })  : _client = client ?? http.Client(),
        _storageHelper = storageHelper;

  // Headers avec authentification
  Future<Map<String, String>> get _headers async {
    final token = await _storageHelper.getToken();
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Récupérer le planning d'un utilisateur
  Future<List<Planning>> getUserPlanning(String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{
        'user_id': userId,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final uri = Uri.parse('${ApiConfig.baseUrl}/planning/user')
          .replace(queryParameters: queryParams);

      final response = await _client.get(uri, headers: await _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final planningsJson = data['plannings'] as List<dynamic>;
        
        return planningsJson
            .map((json) => Planning.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors du chargement du planning');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Récupérer le planning d'équipe (pour les managers)
  Future<List<Planning>> getTeamPlanning(String managerId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{
        'manager_id': managerId,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final uri = Uri.parse('${ApiConfig.baseUrl}/planning/team')
          .replace(queryParameters: queryParams);

      final response = await _client.get(uri, headers: await _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final planningsJson = data['plannings'] as List<dynamic>;
        
        return planningsJson
            .map((json) => Planning.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 403) {
        throw Exception('Accès non autorisé. Seuls les managers peuvent voir le planning d\'équipe.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors du chargement du planning d\'équipe');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Créer un nouveau planning
  Future<Planning> createPlanning(Planning planning) async {
    try {
      final response = await _client.post(
        Uri.parse('${ApiConfig.baseUrl}/planning'),
        headers: await _headers,
        body: json.encode(planning.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return Planning.fromJson(data['planning'] as Map<String, dynamic>);
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 422) {
        final errorData = json.decode(response.body);
        final errors = errorData['errors'] as Map<String, dynamic>;
        final errorMessages = errors.values.expand((e) => e as List).join(', ');
        throw Exception('Données invalides: $errorMessages');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la création du planning');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Mettre à jour un planning
  Future<Planning> updatePlanning(Planning planning) async {
    try {
      final response = await _client.put(
        Uri.parse('${ApiConfig.baseUrl}/planning/${planning.id}'),
        headers: await _headers,
        body: json.encode(planning.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Planning.fromJson(data['planning'] as Map<String, dynamic>);
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 404) {
        throw Exception('Planning non trouvé.');
      } else if (response.statusCode == 422) {
        final errorData = json.decode(response.body);
        final errors = errorData['errors'] as Map<String, dynamic>;
        final errorMessages = errors.values.expand((e) => e as List).join(', ');
        throw Exception('Données invalides: $errorMessages');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la modification du planning');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Supprimer un planning
  Future<void> deletePlanning(String planningId) async {
    try {
      final response = await _client.delete(
        Uri.parse('${ApiConfig.baseUrl}/planning/$planningId'),
        headers: await _headers,
      );

      if (response.statusCode == 200) {
        return;
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 404) {
        throw Exception('Planning non trouvé.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la suppression du planning');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Créer un planning récurrent
  Future<List<Planning>> createRecurrentPlanning({
    required Planning basePlanning,
    required String pattern,
    required DateTime endDate,
  }) async {
    try {
      final requestData = {
        'base_planning': basePlanning.toJson(),
        'pattern': pattern,
        'end_date': endDate.toIso8601String(),
      };

      final response = await _client.post(
        Uri.parse('${ApiConfig.baseUrl}/planning/recurrent'),
        headers: await _headers,
        body: json.encode(requestData),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        final planningsJson = data['plannings'] as List<dynamic>;
        
        return planningsJson
            .map((json) => Planning.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 422) {
        final errorData = json.decode(response.body);
        final errors = errorData['errors'] as Map<String, dynamic>;
        final errorMessages = errors.values.expand((e) => e as List).join(', ');
        throw Exception('Données invalides: $errorMessages');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la création du planning récurrent');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Récupérer les modèles de planning
  Future<List<PlanningTemplate>> getTemplates() async {
    try {
      final response = await _client.get(
        Uri.parse('${ApiConfig.baseUrl}/planning/templates'),
        headers: await _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final templatesJson = data['templates'] as List<dynamic>;
        
        return templatesJson
            .map((json) => PlanningTemplate.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors du chargement des modèles');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Créer un modèle de planning
  Future<PlanningTemplate> createTemplate(PlanningTemplate template) async {
    try {
      final response = await _client.post(
        Uri.parse('${ApiConfig.baseUrl}/planning/templates'),
        headers: await _headers,
        body: json.encode(template.toJson()),
      );

      if (response.statusCode == 201) {
        final data = json.decode(response.body);
        return PlanningTemplate.fromJson(data['template'] as Map<String, dynamic>);
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 422) {
        final errorData = json.decode(response.body);
        final errors = errorData['errors'] as Map<String, dynamic>;
        final errorMessages = errors.values.expand((e) => e as List).join(', ');
        throw Exception('Données invalides: $errorMessages');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la création du modèle');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Mettre à jour un modèle de planning
  Future<PlanningTemplate> updateTemplate(PlanningTemplate template) async {
    try {
      final response = await _client.put(
        Uri.parse('${ApiConfig.baseUrl}/planning/templates/${template.id}'),
        headers: await _headers,
        body: json.encode(template.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return PlanningTemplate.fromJson(data['template'] as Map<String, dynamic>);
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 404) {
        throw Exception('Modèle non trouvé.');
      } else if (response.statusCode == 422) {
        final errorData = json.decode(response.body);
        final errors = errorData['errors'] as Map<String, dynamic>;
        final errorMessages = errors.values.expand((e) => e as List).join(', ');
        throw Exception('Données invalides: $errorMessages');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la modification du modèle');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Supprimer un modèle de planning
  Future<void> deleteTemplate(String templateId) async {
    try {
      final response = await _client.delete(
        Uri.parse('${ApiConfig.baseUrl}/planning/templates/$templateId'),
        headers: await _headers,
      );

      if (response.statusCode == 200) {
        return;
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else if (response.statusCode == 404) {
        throw Exception('Modèle non trouvé.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la suppression du modèle');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Récupérer les conflits de planning
  Future<List<Planning>> getConflicts(Planning planning) async {
    try {
      final response = await _client.post(
        Uri.parse('${ApiConfig.baseUrl}/planning/conflicts'),
        headers: await _headers,
        body: json.encode(planning.toJson()),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final conflictsJson = data['conflicts'] as List<dynamic>;
        
        return conflictsJson
            .map((json) => Planning.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de la vérification des conflits');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Récupérer les statistiques de planning
  Future<Map<String, dynamic>> getPlanningStatistics({
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{
        if (agentId != null) 'agent_id': agentId,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final uri = Uri.parse('${const String.fromEnvironment('API_BASE_URL')}/planning/statistics')
          .replace(queryParameters: queryParams);

      final response = await _client.get(uri, headers: await _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['statistics'] as Map<String, dynamic>;
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors du chargement des statistiques');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  // Exporter le planning
  Future<String> exportPlanning({
    required String format, // 'pdf', 'excel', 'csv'
    String? agentId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{
        'format': format,
        if (agentId != null) 'agent_id': agentId,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
      };

      final uri = Uri.parse('${ApiConfig.baseUrl}/planning/export')
          .replace(queryParameters: queryParams);

      final response = await _client.get(uri, headers: await _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['download_url'] as String;
      } else if (response.statusCode == 401) {
        throw Exception('Session expirée. Veuillez vous reconnecter.');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur lors de l\'export du planning');
      }
    } catch (e) {
      if (e is Exception) rethrow;
      throw Exception('Erreur de connexion: ${e.toString()}');
    }
  }

  void dispose() {
    _client.close();
  }
}