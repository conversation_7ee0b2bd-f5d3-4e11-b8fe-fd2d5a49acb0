import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/agent.dart';
import '../services/sync_service.dart';
import '../services/database_service.dart';

class AuthProvider extends ChangeNotifier {
  static final AuthProvider _instance = AuthProvider._internal();
  factory AuthProvider() => _instance;
  AuthProvider._internal();

  final SyncService _syncService = SyncService();
  final DatabaseService _databaseService = DatabaseService();

  // État de l'authentification
  bool _isAuthenticated = false;
  bool _isLoading = false;
  Agent? _currentAgent;
  String? _errorMessage;

  // Getters
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  Agent? get currentAgent => _currentAgent;
  Agent? get currentUser => _currentAgent;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;
  bool get isManager => _currentAgent?.role == 'manager' || _currentAgent?.role == 'admin';

  /// Initialise le provider et vérifie l'état d'authentification
  Future<void> initialize() async {
    _setLoading(true);
    
    try {
      // Vérifier si l'utilisateur est déjà connecté
      final isAuth = await _syncService.isAuthenticated();
      
      if (isAuth) {
        // Récupérer les informations de l'agent depuis la base locale
        await _loadCurrentAgent();
        _isAuthenticated = true;
      }
    } catch (e) {
      _setError('Erreur lors de l\'initialisation: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Connecte l'utilisateur
  Future<bool> login(String username, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final result = await _syncService.authenticate(username, password);
      
      if (result.success) {
        _currentAgent = result.agent;
        _isAuthenticated = true;
        
        // Sauvegarder les informations de connexion
        await _saveLoginInfo(username);
        
        // Synchroniser les données initiales
        await _syncService.synchronize(forceFullSync: true);
        
        notifyListeners();
        return true;
      } else {
        _setError(result.errorMessage ?? 'Erreur de connexion');
        return false;
      }
    } catch (e) {
      _setError('Erreur de connexion: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Déconnecte l'utilisateur
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      await _syncService.logout();
      await _clearLoginInfo();
      
      _isAuthenticated = false;
      _currentAgent = null;
      _clearError();
      
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de la déconnexion: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Actualise les informations de l'agent
  Future<void> refreshAgent() async {
    if (!_isAuthenticated || _currentAgent == null) return;
    
    try {
      await _loadCurrentAgent();
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors de l\'actualisation: ${e.toString()}');
    }
  }

  /// Met à jour les informations de l'agent
  Future<bool> updateAgent(Agent updatedAgent) async {
    _setLoading(true);
    
    try {
      await _databaseService.insertOrUpdateAgent(updatedAgent);
      _currentAgent = updatedAgent;
      
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la mise à jour: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Vérifie si l'agent est actif
  bool get isAgentActive {
    return _currentAgent?.isActive ?? false;
  }

  /// Vérifie si l'agent a les permissions biométriques activées
  bool get isBiometricEnabled {
    return _currentAgent?.biometricEnabled ?? false;
  }

  /// Active/désactive la biométrie pour l'agent
  Future<bool> toggleBiometric(bool enabled) async {
    if (_currentAgent == null) return false;
    
    try {
      final updatedAgent = _currentAgent!.copyWith(biometricEnabled: enabled);
      return await updateAgent(updatedAgent);
    } catch (e) {
      _setError('Erreur lors de la modification biométrique: ${e.toString()}');
      return false;
    }
  }

  /// Charge les informations de l'agent actuel depuis la base de données
  Future<void> _loadCurrentAgent() async {
    try {
      // Récupérer l'ID de l'agent depuis les préférences
      final prefs = await SharedPreferences.getInstance();
      final agentId = prefs.getString('current_agent_id');
      
      if (agentId != null) {
        final agent = await _databaseService.getAgentById(agentId);
        if (agent != null) {
          _currentAgent = agent;
        }
      }
    } catch (e) {
      throw Exception('Impossible de charger les informations de l\'agent: $e');
    }
  }

  /// Sauvegarde les informations de connexion
  Future<void> _saveLoginInfo(String username) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_username', username);
    
    if (_currentAgent != null) {
      await prefs.setString('current_agent_id', _currentAgent!.id);
    }
  }

  /// Efface les informations de connexion
  Future<void> _clearLoginInfo() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('last_username');
    await prefs.remove('current_agent_id');
  }

  /// Récupère le dernier nom d'utilisateur utilisé
  Future<String?> getLastUsername() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('last_username');
  }

  /// Définit l'état de chargement
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Définit un message d'erreur
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Efface le message d'erreur
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// Efface l'erreur manuellement (pour l'UI)
  void clearError() {
    _clearError();
  }

  /// Vérifie si une reconnexion automatique est possible
  Future<bool> canAutoReconnect() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUsername = prefs.getString('last_username');
      final agentId = prefs.getString('current_agent_id');
      
      return lastUsername != null && agentId != null;
    } catch (e) {
      return false;
    }
  }

  /// Tente une reconnexion automatique
  Future<bool> tryAutoReconnect() async {
    if (!await canAutoReconnect()) return false;
    
    try {
      final isAuth = await _syncService.isAuthenticated();
      if (isAuth) {
        await _loadCurrentAgent();
        _isAuthenticated = true;
        notifyListeners();
        return true;
      }
    } catch (e) {
      // Échec silencieux de la reconnexion automatique
    }
    
    return false;
  }

  @override
  void dispose() {
    // Nettoyage si nécessaire
    super.dispose();
  }
}