import 'package:local_auth/local_auth.dart';
import 'package:local_auth_android/local_auth_android.dart';
import 'package:local_auth_ios/local_auth_ios.dart';

class BiometricService {
  static final BiometricService _instance = BiometricService._internal();
  factory BiometricService() => _instance;
  BiometricService._internal();

  final LocalAuthentication _localAuth = LocalAuthentication();

  /// Vérifie si l'appareil supporte l'authentification biométrique
  Future<bool> isDeviceSupported() async {
    try {
      return await _localAuth.isDeviceSupported();
    } catch (e) {
      return false;
    }
  }

  /// Vérifie si des biométries sont disponibles sur l'appareil
  Future<bool> canCheckBiometrics() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  /// Récupère la liste des biométries disponibles
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      return [];
    }
  }

  /// Vérifie si l'empreinte digitale est disponible
  Future<bool> isFingerprintAvailable() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.fingerprint);
    } catch (e) {
      return false;
    }
  }

  /// Vérifie si la reconnaissance faciale est disponible
  Future<bool> isFaceRecognitionAvailable() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.contains(BiometricType.face);
    } catch (e) {
      return false;
    }
  }

  /// Authentifie l'utilisateur avec la biométrie
  Future<BiometricAuthResult> authenticate({
    String localizedReason = 'Veuillez vous authentifier pour pointer',
    bool useErrorDialogs = true,
    bool stickyAuth = false,
  }) async {
    try {
      // Vérifier si l'appareil supporte la biométrie
      if (!await isDeviceSupported()) {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'L\'appareil ne supporte pas l\'authentification biométrique',
          errorType: BiometricErrorType.deviceNotSupported,
        );
      }

      // Vérifier si la biométrie est disponible
      if (!await canCheckBiometrics()) {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'Aucune biométrie configurée sur cet appareil',
          errorType: BiometricErrorType.noBiometricsEnrolled,
        );
      }

      // Effectuer l'authentification
      final bool didAuthenticate = await _localAuth.authenticate(
        localizedReason: localizedReason,
        authMessages: const [
          AndroidAuthMessages(
            signInTitle: 'Authentification biométrique',
            cancelButton: 'Annuler',
            deviceCredentialsRequiredTitle: 'Authentification requise',
            deviceCredentialsSetupDescription: 'Veuillez configurer l\'authentification sur votre appareil',
            goToSettingsButton: 'Paramètres',
            goToSettingsDescription: 'Aller aux paramètres pour configurer l\'authentification',
          ),
          IOSAuthMessages(
            cancelButton: 'Annuler',
            goToSettingsButton: 'Paramètres',
            goToSettingsDescription: 'Aller aux paramètres pour configurer Touch ID ou Face ID',
            lockOut: 'Veuillez réactiver Touch ID ou Face ID',
          ),
        ],
        options: AuthenticationOptions(
          useErrorDialogs: useErrorDialogs,
          stickyAuth: stickyAuth,
          biometricOnly: true,
        ),
      );

      if (didAuthenticate) {
        return BiometricAuthResult(
          success: true,
          errorMessage: null,
          errorType: null,
        );
      } else {
        return BiometricAuthResult(
          success: false,
          errorMessage: 'Authentification échouée',
          errorType: BiometricErrorType.authenticationFailed,
        );
      }
    } catch (e) {
      String errorMessage;
      BiometricErrorType errorType;

      if (e.toString().contains('UserCancel')) {
        errorMessage = 'Authentification annulée par l\'utilisateur';
        errorType = BiometricErrorType.userCancel;
      } else if (e.toString().contains('LockedOut')) {
        errorMessage = 'Trop de tentatives échouées. Veuillez réessayer plus tard';
        errorType = BiometricErrorType.lockedOut;
      } else if (e.toString().contains('PermanentlyLockedOut')) {
        errorMessage = 'Biométrie définitivement verrouillée. Utilisez votre code PIN';
        errorType = BiometricErrorType.permanentlyLockedOut;
      } else {
        errorMessage = 'Erreur d\'authentification: ${e.toString()}';
        errorType = BiometricErrorType.unknown;
      }

      return BiometricAuthResult(
        success: false,
        errorMessage: errorMessage,
        errorType: errorType,
      );
    }
  }

  /// Authentification rapide pour le pointage
  Future<BiometricAuthResult> authenticateForAttendance() async {
    return await authenticate(
      localizedReason: 'Authentifiez-vous pour enregistrer votre pointage',
      useErrorDialogs: true,
      stickyAuth: true,
    );
  }

  /// Authentification pour l'inscription d'un nouvel agent
  Future<BiometricAuthResult> authenticateForRegistration() async {
    return await authenticate(
      localizedReason: 'Enregistrez votre empreinte pour l\'inscription',
      useErrorDialogs: true,
      stickyAuth: false,
    );
  }

  /// Authentification pour accéder aux fonctions administratives
  Future<BiometricAuthResult> authenticateForAdmin() async {
    return await authenticate(
      localizedReason: 'Authentification administrateur requise',
      useErrorDialogs: true,
      stickyAuth: true,
    );
  }

  /// Arrête l'authentification en cours
  Future<void> stopAuthentication() async {
    try {
      await _localAuth.stopAuthentication();
    } catch (e) {
      // Ignorer les erreurs lors de l'arrêt
    }
  }
}

/// Résultat de l'authentification biométrique
class BiometricAuthResult {
  final bool success;
  final String? errorMessage;
  final BiometricErrorType? errorType;

  BiometricAuthResult({
    required this.success,
    this.errorMessage,
    this.errorType,
  });

  @override
  String toString() {
    return 'BiometricAuthResult{success: $success, errorMessage: $errorMessage, errorType: $errorType}';
  }
}

/// Types d'erreurs biométriques
enum BiometricErrorType {
  deviceNotSupported,
  noBiometricsEnrolled,
  authenticationFailed,
  userCancel,
  lockedOut,
  permanentlyLockedOut,
  unknown,
}