import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../../core/providers/pointage_provider.dart';
import '../../core/models/pointage.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';

class HistoriqueScreen extends StatefulWidget {
  const HistoriqueScreen({super.key});

  @override
  State<HistoriqueScreen> createState() => _HistoriqueScreenState();
}

class _HistoriqueScreenState extends State<HistoriqueScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  DateTime _selectedDate = DateTime.now();
  String _selectedFilter = 'Tous';
  final List<String> _filters = ['Tous', 'Entrée', 'Sortie', 'Pause', 'Retour'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadHistorique();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadHistorique() async {
    final pointageProvider = Provider.of<PointageProvider>(context, listen: false);
    await pointageProvider.loadHistorique();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            _buildAppBar(),
            _buildTabBar(),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildDailyView(),
            _buildWeeklyView(),
            _buildMonthlyView(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'Historique',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list, color: Colors.white),
          onPressed: _showFilterDialog,
        ),
        IconButton(
          icon: const Icon(Icons.calendar_today, color: Colors.white),
          onPressed: _showDatePicker,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return SliverPersistentHeader(
      delegate: _SliverAppBarDelegate(
        TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).primaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).primaryColor,
          tabs: const [
            Tab(text: 'Jour', icon: Icon(Icons.today)),
            Tab(text: 'Semaine', icon: Icon(Icons.view_week)),
            Tab(text: 'Mois', icon: Icon(Icons.calendar_month)),
          ],
        ),
      ),
      pinned: true,
    );
  }

  Widget _buildDailyView() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        if (pointageProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement de l\'historique...'),
          );
        }

        final pointages = _filterPointages(
          pointageProvider.historique.where((p) => _isSameDay(p.dateHeure, _selectedDate)).toList(),
        );

        if (pointages.isEmpty) {
          return EmptyStateWidget(
            icon: Icons.history,
            title: 'Aucun pointage',
            subtitle: 'Aucun pointage trouvé pour cette date.',
            action: ElevatedButton(
              onPressed: _loadHistorique,
              child: const Text('Actualiser'),
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadHistorique,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: pointages.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildDateHeader(_selectedDate, pointages);
              }
              return _buildPointageCard(pointages[index - 1]);
            },
          ),
        );
      },
    );
  }

  Widget _buildWeeklyView() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        if (pointageProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement de l\'historique...'),
          );
        }

        final weekStart = _getWeekStart(_selectedDate);
        final weekDays = List.generate(7, (index) => weekStart.add(Duration(days: index)));
        
        return RefreshIndicator(
          onRefresh: _loadHistorique,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: weekDays.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildWeekHeader(weekStart);
              }
              
              final date = weekDays[index - 1];
              final pointages = _filterPointages(
                pointageProvider.historique.where((p) => _isSameDay(p.dateHeure, date)).toList(),
              );
              
              return _buildDayCard(date, pointages);
            },
          ),
        );
      },
    );
  }

  Widget _buildMonthlyView() {
    return Consumer<PointageProvider>(
      builder: (context, pointageProvider, child) {
        if (pointageProvider.isLoading) {
          return const Center(
            child: LoadingWidget(message: 'Chargement de l\'historique...'),
          );
        }

        final monthStart = DateTime(_selectedDate.year, _selectedDate.month, 1);
        final monthEnd = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
        final monthDays = List.generate(
          monthEnd.day,
          (index) => monthStart.add(Duration(days: index)),
        );
        
        return RefreshIndicator(
          onRefresh: _loadHistorique,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: monthDays.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildMonthHeader(monthStart);
              }
              
              final date = monthDays[index - 1];
              final pointages = _filterPointages(
                pointageProvider.historique.where((p) => _isSameDay(p.dateHeure, date)).toList(),
              );
              
              return _buildDayCard(date, pointages);
            },
          ),
        );
      },
    );
  }

  Widget _buildDateHeader(DateTime date, List<Pointage> pointages) {
    final totalHours = _calculateTotalHours(pointages);
    final isToday = _isSameDay(date, DateTime.now());
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isToday ? Icons.today : Icons.calendar_today,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                DateFormat('EEEE d MMMM yyyy', 'fr_FR').format(date),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              if (isToday) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Aujourd\'hui',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Pointages',
                  pointages.length.toString(),
                  Icons.access_time,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Temps total',
                  totalHours,
                  Icons.schedule,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Statut',
                  _getStatusForDay(pointages),
                  Icons.check_circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWeekHeader(DateTime weekStart) {
    final weekEnd = weekStart.add(const Duration(days: 6));
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.view_week,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Semaine du ${DateFormat('d MMM', 'fr_FR').format(weekStart)} au ${DateFormat('d MMM yyyy', 'fr_FR').format(weekEnd)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMonthHeader(DateTime monthStart) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_month,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(
            DateFormat('MMMM yyyy', 'fr_FR').format(monthStart),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointageCard(Pointage pointage) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getTypeColor(pointage.type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getTypeIcon(pointage.type),
            color: _getTypeColor(pointage.type),
          ),
        ),
        title: Text(
          _getTypeLabel(pointage.type),
          style: const TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              DateFormat('HH:mm:ss').format(pointage.dateHeure),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (pointage.adresse != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(Icons.location_on, size: 14, color: Colors.grey),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      pointage.adresse!,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: pointage.isValidated ? Colors.green : Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                pointage.isValidated ? 'Validé' : 'En attente',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayCard(DateTime date, List<Pointage> pointages) {
    final isToday = _isSameDay(date, DateTime.now());
    final totalHours = _calculateTotalHours(pointages);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isToday ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isToday
            ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: ExpansionTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isToday
                ? Theme.of(context).primaryColor
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            date.day.toString(),
            style: TextStyle(
              color: isToday ? Colors.white : Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          DateFormat('EEEE d MMMM', 'fr_FR').format(date),
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: isToday ? Theme.of(context).primaryColor : null,
          ),
        ),
        subtitle: Text(
          '${pointages.length} pointage(s) • $totalHours',
          style: const TextStyle(fontSize: 12),
        ),
        children: pointages.map((pointage) => _buildPointageCard(pointage)).toList(),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  List<Pointage> _filterPointages(List<Pointage> pointages) {
    if (_selectedFilter == 'Tous') {
      return pointages;
    }
    return pointages.where((p) => _getTypeLabel(p.type) == _selectedFilter).toList();
  }

  String _calculateTotalHours(List<Pointage> pointages) {
    if (pointages.isEmpty) return '0h00';
    
    // Logique simplifiée pour calculer le temps total
    // Dans une vraie application, il faudrait une logique plus complexe
    final entrees = pointages.where((p) => p.type == TypePointage.entree).toList();
    final sorties = pointages.where((p) => p.type == TypePointage.sortie).toList();
    
    if (entrees.isEmpty || sorties.isEmpty) return '0h00';
    
    final debut = entrees.first.dateHeure;
    final fin = sorties.last.dateHeure;
    final duree = fin.difference(debut);
    
    final heures = duree.inHours;
    final minutes = duree.inMinutes % 60;
    
    return '${heures}h${minutes.toString().padLeft(2, '0')}';
  }

  String _getStatusForDay(List<Pointage> pointages) {
    if (pointages.isEmpty) return 'Absent';
    
    final hasEntree = pointages.any((p) => p.type == TypePointage.entree);
    final hasSortie = pointages.any((p) => p.type == TypePointage.sortie);
    
    if (hasEntree && hasSortie) return 'Complet';
    if (hasEntree) return 'En cours';
    return 'Partiel';
  }

  IconData _getTypeIcon(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Icons.login;
      case TypePointage.sortie:
        return Icons.logout;
      case TypePointage.pauseDebut:
        return Icons.pause;
      case TypePointage.pauseFin:
        return Icons.play_arrow;
    }
  }

  Color _getTypeColor(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Colors.green;
      case TypePointage.sortie:
        return Colors.red;
      case TypePointage.pauseDebut:
        return Colors.orange;
      case TypePointage.pauseFin:
        return Colors.blue;
    }
  }

  String _getTypeLabel(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return 'Entrée';
      case TypePointage.sortie:
        return 'Sortie';
      case TypePointage.pauseDebut:
        return 'Pause';
      case TypePointage.pauseFin:
        return 'Retour';
    }
  }

  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtrer par type'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _filters.map((filter) {
            return RadioListTile<String>(
              title: Text(filter),
              value: filter,
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showDatePicker() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      locale: const Locale('fr', 'FR'),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate(this._tabBar);

  final TabBar _tabBar;

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}