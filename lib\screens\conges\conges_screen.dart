import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../core/providers/conge_provider.dart';
// Import supprimé car non utilisé
import '../../core/models/conge.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

// Simple statistics class for UI
class StatistiquesConge {
  final int joursAccordes;
  final int joursUtilises;
  final int joursRestants;
  
  StatistiquesConge({
    required this.joursAccordes,
    required this.joursUtilises,
    required this.joursRestants,
  });
}

class CongesScreen extends StatefulWidget {
  const CongesScreen({super.key});

  @override
  State<CongesScreen> createState() => _CongesScreenState();
}

class _CongesScreenState extends State<CongesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    final congeProvider = Provider.of<CongeProvider>(context, listen: false);
    await Future.wait([
      congeProvider.loadConges(),
      congeProvider.loadStatistiques(),
    ]);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Congés'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Mes congés', icon: Icon(Icons.event_available)),
            Tab(text: 'Statistiques', icon: Icon(Icons.bar_chart)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/conges/demande'),
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCongesTab(),
          _buildStatistiquesTab(),
        ],
      ),
    );
  }

  Widget _buildCongesTab() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        if (congeProvider.isLoading) {
          return const Center(child: LoadingWidget());
        }
        
        if (congeProvider.errorMessage != null) {
          return Center(
            child: CustomErrorWidget(
              title: 'Erreur',
              message: congeProvider.errorMessage!,
              onRetry: _loadData,
            ),
          );
        }
        
        return RefreshIndicator(
          onRefresh: _loadData,
          child: CustomScrollView(
            slivers: [
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    _buildQuickActions(),
                    const SizedBox(height: 16),
                    _buildSoldeCard(),
                    const SizedBox(height: 16),
                    _buildCongesList(),
                    const SizedBox(height: 100), // Espace pour la bottom nav
                  ]),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatistiquesTab() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        if (congeProvider.isLoading) {
          return const Center(child: LoadingWidget());
        }
        
        // Create statistics object from CongeProvider properties
        final stats = StatistiquesConge(
          joursAccordes: congeProvider.joursCongesTotal,
          joursUtilises: congeProvider.joursCongesPris,
          joursRestants: congeProvider.joursCongesRestants,
        );
        
        return RefreshIndicator(
          onRefresh: _loadData,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildStatistiquesAnnuelles(stats),
                const SizedBox(height: 16),
                _buildStatistiquesParType(stats),
                const SizedBox(height: 16),
                _buildGraphiqueUtilisation(stats),
                const SizedBox(height: 100), // Espace pour la bottom nav
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Actions rapides',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildQuickActionButton(
                  icon: Icons.add_circle,
                  label: 'Nouvelle\ndemande',
                  color: Colors.green,
                  onTap: () => context.push('/conges/demande'),
                ),
                _buildQuickActionButton(
                  icon: Icons.calendar_today,
                  label: 'Planning\nannuel',
                  color: Colors.blue,
                  onTap: () => context.push('/planning'),
                ),
                _buildQuickActionButton(
                  icon: Icons.history,
                  label: 'Historique\ncomplet',
                  color: Colors.orange,
                  onTap: () => _showHistoriqueComplet(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Icon(
              icon,
              color: color,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSoldeCard() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        final stats = StatistiquesConge(
          joursAccordes: congeProvider.joursCongesTotal,
          joursUtilises: congeProvider.joursCongesPris,
          joursRestants: congeProvider.joursCongesRestants,
        );
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  Theme.of(context).primaryColor.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.account_balance_wallet,
                      color: Theme.of(context).primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'Solde de congés',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildSoldeItem(
                        'Disponibles',
                        '${stats?.joursRestants ?? 0}',
                        'jours',
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildSoldeItem(
                        'Utilisés',
                        '${stats?.joursUtilises ?? 0}',
                        'jours',
                        Colors.orange,
                      ),
                    ),
                    Expanded(
                      child: _buildSoldeItem(
                        'En attente',
                        '0', // TODO: Add proper statistics
                        'jours',
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSoldeItem(String label, String value, String unit, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            unit,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCongesList() {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        final conges = congeProvider.conges;
        
        if (conges.isEmpty) {
          return Card(
            elevation: 2,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Container(
              padding: const EdgeInsets.all(40),
              child: Column(
                children: [
                  Icon(
                    Icons.event_available,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Aucun congé enregistré',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Commencez par faire une demande de congé',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => context.push('/conges/demande'),
                    icon: const Icon(Icons.add),
                    label: const Text('Nouvelle demande'),
                  ),
                ],
              ),
            ),
          );
        }
        
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Mes congés récents',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${conges.length} demande(s)',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              ...conges.take(5).map((conge) => _buildCongeItem(conge)),
              if (conges.length > 5)
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Center(
                      child: TextButton(
                        onPressed: () => _showHistoriqueComplet(),
                        child: const Text('Voir tout l\'historique'),
                      ),
                    ),
                  ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCongeItem(Conge conge) {
    final color = _getStatutColor(conge.statut);
    final icon = _getTypeIcon(conge.type);
    
    return InkWell(
      onTap: () => _showCongeDetails(conge),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 1,
            ),
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _getTypeLabel(conge.type),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _getStatutLabel(conge.statut),
                          style: TextStyle(
                            color: color,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_formatDate(conge.dateDebut)} - ${_formatDate(conge.dateFin)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '${conge.nombreJours} jour(s)',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistiquesAnnuelles(StatistiquesConge? stats) {
    if (stats == null) return const SizedBox.shrink();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Statistiques annuelles',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total accordé',
                    '${stats.joursAccordes}',
                    Icons.event_available,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Utilisés',
                    '${stats.joursUtilises}',
                    Icons.event_busy,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Restants',
                    '${stats.joursRestants}',
                    Icons.event,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'En attente',
                    '${stats.joursUtilises}',
                    Icons.schedule,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatistiquesParType(StatistiquesConge? stats) {
    if (stats == null) return const SizedBox.shrink();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Répartition par type',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTypeStatItem('Congés payés', 0, Colors.blue), // TODO: Add proper statistics
            _buildTypeStatItem('Congés maladie', 0, Colors.red), // TODO: Add proper statistics
            _buildTypeStatItem('Congés sans solde', 0, Colors.orange), // TODO: Add proper statistics
            _buildTypeStatItem('Congés exceptionnels', 0, Colors.purple), // TODO: Add proper statistics
          ],
        ),
      ),
    );
  }

  Widget _buildTypeStatItem(String label, int jours, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '$jours jour(s)',
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGraphiqueUtilisation(StatistiquesConge? stats) {
    if (stats == null) return const SizedBox.shrink();
    
    final total = stats.joursAccordes;
    final utilises = stats.joursUtilises;
    final pourcentage = total > 0 ? (utilises / total * 100) : 0.0;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Taux d\'utilisation',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${pourcentage.toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                      Text(
                        'de congés utilisés',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 100,
                  height: 100,
                  child: Stack(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.grey[200],
                        ),
                      ),
                      Container(
                        width: 100,
                        height: 100,
                        child: CircularProgressIndicator(
                          value: pourcentage / 100,
                          strokeWidth: 8,
                          backgroundColor: Colors.transparent,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      Center(
                        child: Text(
                          '$utilises/$total',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return 'Congés payés';
      case TypeConge.congeMaladie:
        return 'Congé maladie';
      case TypeConge.congeMaternite:
        return 'Congé maternité';
      case TypeConge.congePaternite:
        return 'Congé paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé sans traitement';
      case TypeConge.absence:
        return 'Absence';
      case TypeConge.annuel:
        return 'Congés annuels';
      case TypeConge.maladie:
        return 'Maladie';
      case TypeConge.maternite:
        return 'Maternité';
      case TypeConge.paternite:
        return 'Paternité';
    }
  }

  String _getStatutLabel(StatutConge statut) {
    switch (statut) {
      case StatutConge.enAttente:
        return 'En attente';
      case StatutConge.approuve:
        return 'Approuvé';
      case StatutConge.rejete:
        return 'Rejeté';
      case StatutConge.refuse:
        return 'Refusé';
      case StatutConge.annule:
        return 'Annulé';
    }
  }

  IconData _getTypeIcon(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return Icons.beach_access;
      case TypeConge.congeMaladie:
        return Icons.local_hospital;
      case TypeConge.congeMaternite:
        return Icons.child_care;
      case TypeConge.congePaternite:
        return Icons.family_restroom;
      case TypeConge.congeSansTraitement:
        return Icons.money_off;
      case TypeConge.absence:
        return Icons.event_busy;
      case TypeConge.annuel:
        return Icons.beach_access;
      case TypeConge.maladie:
        return Icons.local_hospital;
      case TypeConge.maternite:
        return Icons.child_care;
      case TypeConge.paternite:
        return Icons.family_restroom;
    }
  }

  Color _getStatutColor(StatutConge statut) {
    switch (statut) {
      case StatutConge.enAttente:
        return Colors.orange;
      case StatutConge.approuve:
        return Colors.green;
      case StatutConge.refuse:
        return Colors.red;
      case StatutConge.annule:
        return Colors.grey;
      case StatutConge.rejete:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _showHistoriqueComplet() {
    // TODO: Implémenter l'écran d'historique complet
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Historique complet - À implémenter'),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showCongeDetails(Conge conge) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getTypeLabel(conge.type)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Statut', _getStatutLabel(conge.statut)),
            _buildDetailRow('Date de début', _formatDate(conge.dateDebut)),
            _buildDetailRow('Date de fin', _formatDate(conge.dateFin)),
            _buildDetailRow('Nombre de jours', '${conge.nombreJours}'),
            if (conge.motif.isNotEmpty)
              _buildDetailRow('Motif', conge.motif),
            if (conge.commentaireRh?.isNotEmpty == true)
              _buildDetailRow('Commentaire RH', conge.commentaireRh!),
          ],
        ),
        actions: [
          if (conge.statut == StatutConge.enAttente)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _annulerConge(conge);
              },
              child: const Text('Annuler'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
          TextButton(
            onPressed: () => context.push('/conges/details/${conge.id}'),
            child: const Text('Voir détails'),
          ),
        ],
      ),
    );
  }

  Future<void> _annulerConge(Conge conge) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer l\'annulation'),
        content: const Text('Êtes-vous sûr de vouloir annuler cette demande de congé ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Non'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Oui'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        final congeProvider = Provider.of<CongeProvider>(context, listen: false);
        await congeProvider.annulerDemandeConge(conge.id);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Demande de congé annulée'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}