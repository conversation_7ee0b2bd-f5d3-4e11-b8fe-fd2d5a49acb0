import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/pointage_provider.dart';
import '../../core/models/pointage.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class HistoriqueScreen extends StatefulWidget {
  const HistoriqueScreen({super.key});

  @override
  State<HistoriqueScreen> createState() => _HistoriqueScreenState();
}

class _HistoriqueScreenState extends State<HistoriqueScreen> {
  final TextEditingController _searchController = TextEditingController();
  DateTime? _dateDebut;
  DateTime? _dateFin;
  TypePointage? _typeFiltre;
  String _searchQuery = '';
  
  @override
  void initState() {
    super.initState();
    _loadHistorique();
  }

  Future<void> _loadHistorique() async {
    final pointageProvider = Provider.of<PointageProvider>(context, listen: false);
    await pointageProvider.chargerHistorique(
      dateDebut: _dateDebut,
      dateFin: _dateFin,
    );
  }

  List<Pointage> _filtrerPointages(List<Pointage> pointages) {
    return pointages.where((pointage) {
      // Filtre par recherche
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchAdresse = pointage.adresse?.toLowerCase().contains(query) ?? false;
        final matchType = _getTypeLabel(pointage.type).toLowerCase().contains(query);
        if (!matchAdresse && !matchType) {
          return false;
        }
      }
      
      // Filtre par type
      if (_typeFiltre != null && pointage.type != _typeFiltre) {
        return false;
      }
      
      // Filtre par date
      if (_dateDebut != null) {
        final datePointage = DateTime(
          pointage.datePointage.year,
          pointage.datePointage.month,
          pointage.datePointage.day,
        );
        if (datePointage.isBefore(_dateDebut!)) {
          return false;
        }
      }
      
      if (_dateFin != null) {
        final datePointage = DateTime(
          pointage.datePointage.year,
          pointage.datePointage.month,
          pointage.datePointage.day,
        );
        if (datePointage.isAfter(_dateFin!)) {
          return false;
        }
      }
      
      return true;
    }).toList();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: _dateDebut != null && _dateFin != null
          ? DateTimeRange(start: _dateDebut!, end: _dateFin!)
          : null,
      locale: const Locale('fr', 'FR'),
    );
    
    if (picked != null) {
      setState(() {
        _dateDebut = picked.start;
        _dateFin = picked.end;
      });
      await _loadHistorique();
    }
  }

  void _clearFilters() {
    setState(() {
      _dateDebut = null;
      _dateFin = null;
      _typeFiltre = null;
      _searchQuery = '';
      _searchController.clear();
    });
    _loadHistorique();
  }

  String _getTypeLabel(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return 'Entrée';
      case TypePointage.sortie:
        return 'Sortie';
      case TypePointage.pauseDebut:
        return 'Début pause';
      case TypePointage.pauseFin:
        return 'Fin pause';
    }
  }

  IconData _getTypeIcon(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Icons.login;
      case TypePointage.sortie:
        return Icons.logout;
      case TypePointage.pauseDebut:
        return Icons.pause;
      case TypePointage.pauseFin:
        return Icons.play_arrow;
    }
  }

  Color _getTypeColor(TypePointage type) {
    switch (type) {
      case TypePointage.entree:
        return Colors.green;
      case TypePointage.sortie:
        return Colors.red;
      case TypePointage.pauseDebut:
        return Colors.orange;
      case TypePointage.pauseFin:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Historique des pointages'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilters(),
          Expanded(
            child: Consumer<PointageProvider>(
              builder: (context, pointageProvider, child) {
                if (pointageProvider.isLoading) {
                  return const Center(child: LoadingWidget());
                }
                
                if (pointageProvider.errorMessage != null) {
                  return Center(
                    child: CustomErrorWidget(
                      message: pointageProvider.errorMessage!,
                      onRetry: _loadHistorique,
                    ),
                  );
                }
                
                final pointagesFiltres = _filtrerPointages(pointageProvider.historique);
                
                if (pointagesFiltres.isEmpty) {
                  return _buildEmptyState();
                }
                
                return RefreshIndicator(
                  onRefresh: _loadHistorique,
                  child: _buildPointagesList(pointagesFiltres),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Barre de recherche
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Rechercher par lieu ou type...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Theme.of(context).primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 12),
          
          // Filtres actifs
          if (_dateDebut != null || _dateFin != null || _typeFiltre != null)
            _buildActiveFilters(),
        ],
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      width: double.infinity,
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: [
          if (_dateDebut != null && _dateFin != null)
            _buildFilterChip(
              label: 'Du ${_formatDate(_dateDebut!)} au ${_formatDate(_dateFin!)}',
              onDeleted: () {
                setState(() {
                  _dateDebut = null;
                  _dateFin = null;
                });
                _loadHistorique();
              },
            ),
          if (_typeFiltre != null)
            _buildFilterChip(
              label: _getTypeLabel(_typeFiltre!),
              onDeleted: () {
                setState(() {
                  _typeFiltre = null;
                });
              },
            ),
          TextButton.icon(
            onPressed: _clearFilters,
            icon: const Icon(Icons.clear_all, size: 16),
            label: const Text('Effacer tout'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
              textStyle: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required VoidCallback onDeleted,
  }) {
    return Chip(
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onDeleted,
      backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
      deleteIconColor: Theme.of(context).primaryColor,
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun pointage trouvé',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Essayez de modifier vos filtres de recherche',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointagesList(List<Pointage> pointages) {
    // Grouper par date
    final Map<String, List<Pointage>> pointagesParDate = {};
    for (final pointage in pointages) {
      final dateKey = _formatDate(pointage.datePointage);
      pointagesParDate.putIfAbsent(dateKey, () => []).add(pointage);
    }
    
    final dates = pointagesParDate.keys.toList()
      ..sort((a, b) => b.compareTo(a)); // Tri décroissant
    
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: dates.length,
      itemBuilder: (context, index) {
        final date = dates[index];
        final pointagesDuJour = pointagesParDate[date]!
          ..sort((a, b) => b.heurePointage.compareTo(a.heurePointage));
        
        return _buildDateGroup(date, pointagesDuJour);
      },
    );
  }

  Widget _buildDateGroup(String date, List<Pointage> pointages) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            date,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
        ...pointages.map((pointage) => _buildPointageCard(pointage)),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildPointageCard(Pointage pointage) {
    final color = _getTypeColor(pointage.type);
    final icon = _getTypeIcon(pointage.type);
    final label = _getTypeLabel(pointage.type);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showPointageDetails(pointage),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          label,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: color,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          _formatTime(pointage.heurePointage),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.grey[700],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    if (pointage.adresse != null)
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              pointage.adresse!,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        if (pointage.validationBiometrique)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.verified,
                                  size: 12,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'Vérifié',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.green,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (pointage.synchronise)
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.cloud_done,
                                  size: 12,
                                  color: Colors.blue,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  'Sync',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.blue,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filtres'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.date_range),
              title: const Text('Période'),
              subtitle: _dateDebut != null && _dateFin != null
                  ? Text('Du ${_formatDate(_dateDebut!)} au ${_formatDate(_dateFin!)}')
                  : const Text('Toutes les dates'),
              onTap: () {
                Navigator.of(context).pop();
                _selectDateRange();
              },
            ),
            ListTile(
              leading: const Icon(Icons.category),
              title: const Text('Type de pointage'),
              subtitle: Text(_typeFiltre != null ? _getTypeLabel(_typeFiltre!) : 'Tous les types'),
              onTap: () {
                Navigator.of(context).pop();
                _showTypeFilterDialog();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearFilters();
            },
            child: const Text('Effacer'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showTypeFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Type de pointage'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<TypePointage?>(
              title: const Text('Tous les types'),
              value: null,
              groupValue: _typeFiltre,
              onChanged: (value) {
                setState(() {
                  _typeFiltre = value;
                });
                Navigator.of(context).pop();
              },
            ),
            ...TypePointage.values.map((type) => RadioListTile<TypePointage?>(
              title: Text(_getTypeLabel(type)),
              value: type,
              groupValue: _typeFiltre,
              onChanged: (value) {
                setState(() {
                  _typeFiltre = value;
                });
                Navigator.of(context).pop();
              },
            )),
          ],
        ),
      ),
    );
  }

  void _showPointageDetails(Pointage pointage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_getTypeLabel(pointage.type)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Date', _formatDate(pointage.datePointage)),
            _buildDetailRow('Heure', _formatTime(pointage.heurePointage)),
            if (pointage.adresse != null)
              _buildDetailRow('Lieu', pointage.adresse!),
            if (pointage.latitude != null && pointage.longitude != null)
              _buildDetailRow(
                'Coordonnées',
                '${pointage.latitude!.toStringAsFixed(6)}, ${pointage.longitude!.toStringAsFixed(6)}',
              ),
            _buildDetailRow(
              'Validation biométrique',
              pointage.validationBiometrique ? 'Oui' : 'Non',
            ),
            _buildDetailRow(
              'Synchronisé',
              pointage.synchronise ? 'Oui' : 'Non',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}