class CongeStatistiques {
  final int joursCongesAnnuels;
  final int joursCongesPris;
  final int joursCongesRestants;
  final int joursCongesMaladie;
  final int joursCongesMaternite;
  final int joursCongesPaternite;
  final int joursCongesSansTraitement;
  final int joursAbsences;
  final double tauxUtilisation;

  CongeStatistiques({
    required this.joursCongesAnnuels,
    required this.joursCongesPris,
    required this.joursCongesRestants,
    this.joursCongesMaladie = 0,
    this.joursCongesMaternite = 0,
    this.joursCongesPaternite = 0,
    this.joursCongesSansTraitement = 0,
    this.joursAbsences = 0,
  }) : tauxUtilisation = joursCongesAnnuels > 0 
           ? (joursCongesPris / joursCongesAnnuels) * 100 
           : 0.0;

  factory CongeStatistiques.fromJson(Map<String, dynamic> json) {
    return CongeStatistiques(
      joursCongesAnnuels: json['jours_conges_annuels'] ?? 0,
      joursCongesPris: json['jours_conges_pris'] ?? 0,
      joursCongesRestants: json['jours_conges_restants'] ?? 0,
      joursCongesMaladie: json['jours_conges_maladie'] ?? 0,
      joursCongesMaternite: json['jours_conges_maternite'] ?? 0,
      joursCongesPaternite: json['jours_conges_paternite'] ?? 0,
      joursCongesSansTraitement: json['jours_conges_sans_traitement'] ?? 0,
      joursAbsences: json['jours_absences'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'jours_conges_annuels': joursCongesAnnuels,
      'jours_conges_pris': joursCongesPris,
      'jours_conges_restants': joursCongesRestants,
      'jours_conges_maladie': joursCongesMaladie,
      'jours_conges_maternite': joursCongesMaternite,
      'jours_conges_paternite': joursCongesPaternite,
      'jours_conges_sans_traitement': joursCongesSansTraitement,
      'jours_absences': joursAbsences,
      'taux_utilisation': tauxUtilisation,
    };
  }

  CongeStatistiques copyWith({
    int? joursCongesAnnuels,
    int? joursCongesPris,
    int? joursCongesRestants,
    int? joursCongesMaladie,
    int? joursCongesMaternite,
    int? joursCongesPaternite,
    int? joursCongesSansTraitement,
    int? joursAbsences,
  }) {
    return CongeStatistiques(
      joursCongesAnnuels: joursCongesAnnuels ?? this.joursCongesAnnuels,
      joursCongesPris: joursCongesPris ?? this.joursCongesPris,
      joursCongesRestants: joursCongesRestants ?? this.joursCongesRestants,
      joursCongesMaladie: joursCongesMaladie ?? this.joursCongesMaladie,
      joursCongesMaternite: joursCongesMaternite ?? this.joursCongesMaternite,
      joursCongesPaternite: joursCongesPaternite ?? this.joursCongesPaternite,
      joursCongesSansTraitement: joursCongesSansTraitement ?? this.joursCongesSansTraitement,
      joursAbsences: joursAbsences ?? this.joursAbsences,
    );
  }

  @override
  String toString() {
    return 'CongeStatistiques(joursCongesAnnuels: $joursCongesAnnuels, joursCongesPris: $joursCongesPris, joursCongesRestants: $joursCongesRestants, tauxUtilisation: ${tauxUtilisation.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CongeStatistiques &&
        other.joursCongesAnnuels == joursCongesAnnuels &&
        other.joursCongesPris == joursCongesPris &&
        other.joursCongesRestants == joursCongesRestants &&
        other.joursCongesMaladie == joursCongesMaladie &&
        other.joursCongesMaternite == joursCongesMaternite &&
        other.joursCongesPaternite == joursCongesPaternite &&
        other.joursCongesSansTraitement == joursCongesSansTraitement &&
        other.joursAbsences == joursAbsences;
  }

  @override
  int get hashCode {
    return Object.hash(
      joursCongesAnnuels,
      joursCongesPris,
      joursCongesRestants,
      joursCongesMaladie,
      joursCongesMaternite,
      joursCongesPaternite,
      joursCongesSansTraitement,
      joursAbsences,
    );
  }
}
