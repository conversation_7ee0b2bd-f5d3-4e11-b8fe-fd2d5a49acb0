import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../../screens/auth/login_screen.dart';
import '../../screens/auth/splash_screen.dart';
import '../../screens/home/<USER>';
import '../../screens/pointage/pointage_screen.dart';
import '../../screens/pointage/historique_screen.dart';
import '../../screens/conges/conges_screen.dart';
import '../../screens/conges/demande_conge_screen.dart';
import '../../screens/agents/agents_screen.dart';
import '../../screens/agents/profil_screen.dart';
import '../../screens/statistiques/statistiques_screen.dart';
import '../../screens/settings/settings_screen.dart';

class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final isAuthenticated = authProvider.isAuthenticated;
      final isLoading = authProvider.isLoading;

      // Si on est en train de charger, rester sur splash
      if (isLoading) {
        return '/splash';
      }

      // Routes publiques (accessibles sans authentification)
      final publicRoutes = ['/splash', '/login'];
      final isPublicRoute = publicRoutes.contains(state.uri.path);

      // Si pas authentifié et on essaie d'accéder à une route privée
      if (!isAuthenticated && !isPublicRoute) {
        return '/login';
      }

      // Si authentifié et on est sur splash ou login, rediriger vers home
      if (isAuthenticated &&
          (state.uri.path == '/splash' || state.uri.path == '/login')) {
        return '/home';
      }

      // Pas de redirection nécessaire
      return null;
    },
    routes: [
      // Route de démarrage
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Authentification
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),

      // Navigation principale avec shell
      ShellRoute(
        builder: (context, state, child) {
          return MainNavigationShell(child: child);
        },
        routes: [
          // Accueil
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),

          // Pointage
          GoRoute(
            path: '/pointage',
            name: 'pointage',
            builder: (context, state) => const PointageScreen(),
            routes: [
              GoRoute(
                path: 'historique',
                name: 'historique',
                builder: (context, state) => const HistoriqueScreen(),
              ),
            ],
          ),

          // Congés
          GoRoute(
            path: '/conges',
            name: 'conges',
            builder: (context, state) => const CongesScreen(),
            routes: [
              GoRoute(
                path: 'demande',
                name: 'demande-conge',
                builder: (context, state) {
                  return const DemandeCongeScreen();
                },
              ),
            ],
          ),

          // Agents (pour les superviseurs)
          GoRoute(
            path: '/agents',
            name: 'agents',
            builder: (context, state) => const AgentsScreen(),
          ),

          // Profil
          GoRoute(
            path: '/profil',
            name: 'profil',
            builder: (context, state) => const ProfilScreen(),
          ),

          // Statistiques
          GoRoute(
            path: '/statistiques',
            name: 'statistiques',
            builder: (context, state) => const StatistiquesScreen(),
          ),

          // Paramètres
          GoRoute(
            path: '/settings',
            name: 'settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );

  static GoRouter get router => _router;
}

/// Shell de navigation principal avec bottom navigation
class MainNavigationShell extends StatefulWidget {
  final Widget child;

  const MainNavigationShell({super.key, required this.child});

  @override
  State<MainNavigationShell> createState() => _MainNavigationShellState();
}

class _MainNavigationShellState extends State<MainNavigationShell> {
  int _currentIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.home_outlined,
      selectedIcon: Icons.home,
      label: 'Accueil',
      route: '/home',
    ),
    NavigationItem(
      icon: Icons.fingerprint_outlined,
      selectedIcon: Icons.fingerprint,
      label: 'Pointage',
      route: '/pointage',
    ),
    NavigationItem(
      icon: Icons.event_available_outlined,
      selectedIcon: Icons.event_available,
      label: 'Congés',
      route: '/conges',
    ),
    NavigationItem(
      icon: Icons.bar_chart_outlined,
      selectedIcon: Icons.bar_chart,
      label: 'Stats',
      route: '/statistiques',
    ),
    NavigationItem(
      icon: Icons.person_outline,
      selectedIcon: Icons.person,
      label: 'Profil',
      route: '/profil',
    ),
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateCurrentIndex();
  }

  void _updateCurrentIndex() {
    final location = GoRouterState.of(context).uri.path;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        if (_currentIndex != i) {
          setState(() {
            _currentIndex = i;
          });
        }
        break;
      }
    }
  }

  void _onItemTapped(int index) {
    if (index != _currentIndex) {
      context.go(_navigationItems[index].route);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: NavigationBar(
        selectedIndex: _currentIndex,
        onDestinationSelected: _onItemTapped,
        destinations:
            _navigationItems
                .map(
                  (item) => NavigationDestination(
                    icon: Icon(item.icon),
                    selectedIcon: Icon(item.selectedIcon),
                    label: item.label,
                  ),
                )
                .toList(),
      ),
    );
  }
}

/// Item de navigation
class NavigationItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final String route;

  const NavigationItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    required this.route,
  });
}

/// Écran d'erreur
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({super.key, this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Erreur'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              const Text(
                'Une erreur s\'est produite',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                error?.toString() ?? 'Erreur inconnue',
                style: const TextStyle(fontSize: 16, color: Colors.grey),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.go('/home');
                },
                child: const Text('Retour à l\'accueil'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Extensions utiles pour la navigation
extension AppRouterExtension on BuildContext {
  /// Navigue vers la page de pointage
  void goToPointage() => go('/pointage');

  /// Navigue vers l'historique des pointages
  void goToHistorique() => go('/pointage/historique');

  /// Navigue vers les congés
  void goToConges() => go('/conges');

  /// Navigue vers une nouvelle demande de congé
  void goToDemandeConge({String? congeId}) {
    final uri = Uri(
      path: '/conges/demande',
      queryParameters: congeId != null ? {'id': congeId} : null,
    );
    go(uri.toString());
  }

  /// Navigue vers les statistiques
  void goToStatistiques() => go('/statistiques');

  /// Navigue vers le profil
  void goToProfil() => go('/profil');

  /// Navigue vers les paramètres
  void goToSettings() => go('/settings');

  /// Déconnecte et navigue vers login
  void logout() {
    Provider.of<AuthProvider>(this, listen: false).logout();
    go('/login');
  }
}
