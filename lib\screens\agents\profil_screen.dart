import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/models/agent.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';

class ProfilScreen extends StatelessWidget {
  final String? agentId;

  const ProfilScreen({super.key, this.agentId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Profil Agent'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (authProvider.isLoading) {
            return const LoadingWidget();
          }

          if (authProvider.error != null) {
            return CustomErrorWidget(
              message: authProvider.error!,
              onRetry: () => authProvider.loadCurrentAgent(),
            );
          }

          final agent = authProvider.currentAgent;
          if (agent == null) {
            return const CustomErrorWidget(
              message: 'Aucun agent trouvé',
            );
          }

          return _buildProfilContent(context, agent);
        },
      ),
    );
  }

  Widget _buildProfilContent(BuildContext context, Agent agent) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Photo de profil
          Center(
            child: CircleAvatar(
              radius: 60,
              backgroundImage: agent.photoUrl != null
                  ? NetworkImage(agent.photoUrl!)
                  : null,
              child: agent.photoUrl == null
                  ? const Icon(Icons.person, size: 60)
                  : null,
            ),
          ),
          const SizedBox(height: 24),

          // Informations personnelles
          _buildSection(
            'Informations personnelles',
            [
              _buildInfoRow('Nom', '${agent.nom} ${agent.prenom}'),
              _buildInfoRow('Matricule', agent.matricule),
              _buildInfoRow('Email', agent.email),
              _buildInfoRow('Téléphone', agent.telephone),
            ],
          ),

          const SizedBox(height: 24),

          // Informations professionnelles
          _buildSection(
            'Informations professionnelles',
            [
              _buildInfoRow('Poste', agent.poste),
              _buildInfoRow('Département', agent.departement),
              _buildInfoRow('Lieu de rattachement', agent.lieuRattachement),
              _buildInfoRow('Rôle', agent.role),
              _buildInfoRow('Date d\'embauche', 
                '${agent.dateEmbauche.day}/${agent.dateEmbauche.month}/${agent.dateEmbauche.year}'),
            ],
          ),

          const SizedBox(height: 24),

          // Paramètres
          _buildSection(
            'Paramètres',
            [
              _buildInfoRow('Authentification biométrique', 
                agent.biometricEnabled ? 'Activée' : 'Désactivée'),
              _buildInfoRow('Jours de congés annuels', 
                '${agent.joursCongesAnnuels} jours'),
              _buildInfoRow('Statut', agent.statut.name),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
