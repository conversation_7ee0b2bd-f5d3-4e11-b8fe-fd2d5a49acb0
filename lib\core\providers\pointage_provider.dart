import 'package:flutter/foundation.dart';
import '../models/pointage.dart';
import '../services/pointage_service.dart';
import '../services/database_service.dart';
import '../services/sync_service.dart';
import 'auth_provider.dart';

class PointageProvider extends ChangeNotifier {
  final PointageService _pointageService = PointageService();
  final DatabaseService _databaseService = DatabaseService();
  final SyncService _syncService = SyncService();
  final AuthProvider _authProvider = AuthProvider();

  // État du pointage
  bool _isLoading = false;
  String? _errorMessage;
  List<Pointage> _pointagesDuJour = [];
  List<Pointage> _historique = [];
  Pointage? _dernierPointage;

  // Statistiques
  double _heuresTravailleesAujourdhui = 0.0;
  double _heuresTravailleesSemaine = 0.0;
  double _heuresTravailleesMois = 0.0;

  // État de la synchronisation
  bool _isSyncing = false;
  DateTime? _lastSyncTime;

  // Getters
  bool get isLoading => _isLoading;
  bool get isSyncing => _isSyncing;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;
  List<Pointage> get pointagesDuJour => _pointagesDuJour;
  List<Pointage> get historique => _historique;
  Pointage? get dernierPointage => _dernierPointage;
  double get heuresTravailleesAujourdhui => _heuresTravailleesAujourdhui;
  double get heuresTravailleesSemaine => _heuresTravailleesSemaine;
  double get heuresTravailleesMois => _heuresTravailleesMois;
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Initialise le provider
  Future<void> initialize() async {
    await loadPointagesDuJour();
    await loadStatistiques();
  }

  /// Effectue un pointage
  Future<bool> effectuerPointage({
    required TypePointage type,
    String? commentaire,
    bool forcerSansGeolocalisation = false,
  }) async {
    if (_authProvider.currentAgent == null) {
      _setError('Aucun agent connecté');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      final result = await _pointageService.effectuerPointage(
        agentId: _authProvider.currentAgent!.id,
        type: type,
        commentaire: commentaire,
        forcerSansGeolocalisation: forcerSansGeolocalisation,
      );

      if (result.success) {
        // Actualiser les données locales
        await loadPointagesDuJour();
        await loadStatistiques();

        // Tenter une synchronisation en arrière-plan
        _syncInBackground();

        return true;
      } else {
        _setError(result.errorMessage ?? 'Erreur lors du pointage');
        return false;
      }
    } catch (e) {
      _setError('Erreur lors du pointage: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Charge les pointages du jour
  Future<void> loadPointagesDuJour() async {
    if (_authProvider.currentAgent == null) return;

    try {
      final aujourdhui = DateTime.now();
      final pointages = await _pointageService.getPointagesAgent(
        _authProvider.currentAgent!.id,
        date: aujourdhui,
      );

      _pointagesDuJour = pointages;
      _dernierPointage = pointages.isNotEmpty ? pointages.last : null;

      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement des pointages: ${e.toString()}');
    }
  }

  /// Charge l'historique des pointages
  Future<void> loadHistorique({DateTime? dateDebut, DateTime? dateFin}) async {
    if (_authProvider.currentAgent == null) return;

    _setLoading(true);

    try {
      // Si aucune date n'est spécifiée, charger les 30 derniers jours
      dateFin ??= DateTime.now();
      dateDebut ??= dateFin.subtract(const Duration(days: 30));

      final pointages = await _databaseService.getPointagesByDateRange(
        _authProvider.currentAgent!.id,
        dateDebut,
        dateFin,
      );

      _historique = pointages;
      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du chargement de l\'historique: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Charge les statistiques de travail
  Future<void> loadStatistiques() async {
    if (_authProvider.currentAgent == null) return;

    try {
      final agentId = _authProvider.currentAgent!.id;
      final maintenant = DateTime.now();

      // Heures travaillées aujourd'hui
      _heuresTravailleesAujourdhui = await _pointageService
          .calculerHeuresTravaillees(agentId, maintenant);

      // Heures travaillées cette semaine
      final debutSemaine = maintenant.subtract(
        Duration(days: maintenant.weekday - 1),
      );
      _heuresTravailleesSemaine = await _calculerHeuresPeriode(
        agentId,
        debutSemaine,
        maintenant,
      );

      // Heures travaillées ce mois
      final debutMois = DateTime(maintenant.year, maintenant.month, 1);
      _heuresTravailleesMois = await _calculerHeuresPeriode(
        agentId,
        debutMois,
        maintenant,
      );

      notifyListeners();
    } catch (e) {
      _setError('Erreur lors du calcul des statistiques: ${e.toString()}');
    }
  }

  /// Calcule les heures travaillées sur une période
  Future<double> _calculerHeuresPeriode(
    String agentId,
    DateTime debut,
    DateTime fin,
  ) async {
    double totalHeures = 0.0;

    for (
      DateTime date = debut;
      date.isBefore(fin) || date.isAtSameMomentAs(fin);
      date = date.add(const Duration(days: 1))
    ) {
      final heures = await _pointageService.calculerHeuresTravaillees(
        agentId,
        date,
      );
      totalHeures += heures;
    }

    return totalHeures;
  }

  /// Synchronise les données avec le serveur
  Future<bool> synchronize({bool forceFullSync = false}) async {
    _setSyncing(true);
    _clearError();

    try {
      final result = await _syncService.synchronize(
        forceFullSync: forceFullSync,
      );

      if (result.success) {
        _lastSyncTime = DateTime.now();

        // Recharger les données locales après synchronisation
        await loadPointagesDuJour();
        await loadStatistiques();

        return true;
      } else {
        _setError(result.errorMessage ?? 'Erreur de synchronisation');
        return false;
      }
    } catch (e) {
      _setError('Erreur de synchronisation: ${e.toString()}');
      return false;
    } finally {
      _setSyncing(false);
    }
  }

  /// Synchronisation en arrière-plan (silencieuse)
  void _syncInBackground() {
    _syncService
        .synchronize()
        .then((result) {
          if (result.success) {
            _lastSyncTime = DateTime.now();
            notifyListeners();
          }
        })
        .catchError((e) {
          // Erreur silencieuse en arrière-plan
          debugPrint('Erreur de synchronisation en arrière-plan: $e');
        });
  }

  /// Vérifie l'état actuel du pointage
  StatutPointage get statutActuel {
    if (_pointagesDuJour.isEmpty) {
      return StatutPointage.nonPointe;
    }

    final pointagesOrdonnes = List<Pointage>.from(_pointagesDuJour)
      ..sort((a, b) => a.dateHeure.compareTo(b.dateHeure));

    final dernierType = pointagesOrdonnes.last.type;

    switch (dernierType) {
      case TypePointage.entree:
        return StatutPointage.present;
      case TypePointage.sortie:
        return StatutPointage.absent;
      case TypePointage.pauseDebut:
        return StatutPointage.enPause;
      case TypePointage.pauseFin:
        return StatutPointage.present;
    }
  }

  /// Vérifie si un type de pointage est autorisé
  bool isPointageAutorise(TypePointage type) {
    final statut = statutActuel;

    switch (type) {
      case TypePointage.entree:
        return statut == StatutPointage.nonPointe ||
            statut == StatutPointage.absent;
      case TypePointage.sortie:
        return statut == StatutPointage.present;
      case TypePointage.pauseDebut:
        return statut == StatutPointage.present;
      case TypePointage.pauseFin:
        return statut == StatutPointage.enPause;
    }
  }

  /// Obtient le prochain type de pointage suggéré
  TypePointage? get prochainPointageSuggere {
    final statut = statutActuel;

    switch (statut) {
      case StatutPointage.nonPointe:
      case StatutPointage.absent:
        return TypePointage.entree;
      case StatutPointage.present:
        return TypePointage.sortie; // ou pauseDebut selon le contexte
      case StatutPointage.enPause:
        return TypePointage.pauseFin;
    }
  }

  /// Obtient les pointages par date
  Future<List<Pointage>> getPointagesByDate(DateTime date) async {
    if (_authProvider.currentAgent == null) return [];

    return await _pointageService.getPointagesAgent(
      _authProvider.currentAgent!.id,
      date: date,
    );
  }

  /// Définit l'état de chargement
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Définit l'état de synchronisation
  void _setSyncing(bool syncing) {
    if (_isSyncing != syncing) {
      _isSyncing = syncing;
      notifyListeners();
    }
  }

  /// Définit un message d'erreur
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  /// Efface le message d'erreur
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// Efface l'erreur manuellement (pour l'UI)
  void clearError() {
    _clearError();
  }

  /// Actualise toutes les données
  Future<void> refresh() async {
    await loadPointagesDuJour();
    await loadStatistiques();
  }
}

/// Statut actuel du pointage de l'agent
enum StatutPointage { nonPointe, present, absent, enPause }

/// Extensions pour les statuts
extension StatutPointageExtension on StatutPointage {
  String get label {
    switch (this) {
      case StatutPointage.nonPointe:
        return 'Non pointé';
      case StatutPointage.present:
        return 'Présent';
      case StatutPointage.absent:
        return 'Absent';
      case StatutPointage.enPause:
        return 'En pause';
    }
  }

  String get description {
    switch (this) {
      case StatutPointage.nonPointe:
        return 'Aucun pointage effectué aujourd\'hui';
      case StatutPointage.present:
        return 'Vous êtes actuellement au travail';
      case StatutPointage.absent:
        return 'Vous avez terminé votre journée';
      case StatutPointage.enPause:
        return 'Vous êtes actuellement en pause';
    }
  }
}
