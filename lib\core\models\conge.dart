enum TypeConge {
  congeAnnuel,
  congeMaladie,
  congeMaternite,
  congePaternite,
  congeSansTraitement,
  absence,
  // Aliases for compatibility
  annuel,
  maladie,
  maternite,
  paternite,
}

class SoldeConge {
  final String id;
  final String agentId;
  final TypeConge type;
  final double soldeInitial;
  final double soldeUtilise;
  final double soldeRestant;
  final DateTime annee;
  final DateTime? dateExpiration;
  final DateTime createdAt;
  final DateTime updatedAt;

  SoldeConge({
    required this.id,
    required this.agentId,
    required this.type,
    required this.soldeInitial,
    required this.soldeUtilise,
    required this.soldeRestant,
    required this.annee,
    this.dateExpiration,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isExpiringSoon {
    if (dateExpiration == null) return false;
    final maintenant = DateTime.now();
    final difference = dateExpiration!.difference(maintenant).inDays;
    return difference <= 30 && difference > 0;
  }

  factory SoldeConge.fromJson(Map<String, dynamic> json) {
    return SoldeConge(
      id: json['id'],
      agentId: json['agent_id'],
      type: TypeConge.values[json['type']],
      soldeInitial: json['solde_initial'].toDouble(),
      soldeUtilise: json['solde_utilise'].toDouble(),
      soldeRestant: json['solde_restant'].toDouble(),
      annee: DateTime.parse(json['annee']),
      dateExpiration:
          json['date_expiration'] != null
              ? DateTime.parse(json['date_expiration'])
              : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agent_id': agentId,
      'type': type.index,
      'solde_initial': soldeInitial,
      'solde_utilise': soldeUtilise,
      'solde_restant': soldeRestant,
      'annee': annee.toIso8601String(),
      'date_expiration': dateExpiration?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

class CongeStatistiques {
  final int totalConges;
  final int congesApprouves;
  final int congesEnAttente;
  final int congesRefuses;
  final double joursUtilises;
  final double joursRestants;
  final Map<TypeConge, int> repartitionParType;
  final Map<String, int> repartitionParMois;

  CongeStatistiques({
    required this.totalConges,
    required this.congesApprouves,
    required this.congesEnAttente,
    required this.congesRefuses,
    required this.joursUtilises,
    required this.joursRestants,
    required this.repartitionParType,
    required this.repartitionParMois,
  });

  double get tauxUtilisation {
    final total = joursUtilises + joursRestants;
    return total > 0 ? (joursUtilises / total) * 100 : 0;
  }

  factory CongeStatistiques.fromJson(Map<String, dynamic> json) {
    return CongeStatistiques(
      totalConges: json['total_conges'],
      congesApprouves: json['conges_approuves'],
      congesEnAttente: json['conges_en_attente'],
      congesRefuses: json['conges_refuses'],
      joursUtilises: json['jours_utilises'].toDouble(),
      joursRestants: json['jours_restants'].toDouble(),
      repartitionParType: Map<TypeConge, int>.from(
        json['repartition_par_type'].map(
          (key, value) => MapEntry(TypeConge.values[int.parse(key)], value),
        ),
      ),
      repartitionParMois: Map<String, int>.from(json['repartition_par_mois']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_conges': totalConges,
      'conges_approuves': congesApprouves,
      'conges_en_attente': congesEnAttente,
      'conges_refuses': congesRefuses,
      'jours_utilises': joursUtilises,
      'jours_restants': joursRestants,
      'repartition_par_type': repartitionParType.map(
        (key, value) => MapEntry(key.index.toString(), value),
      ),
      'repartition_par_mois': repartitionParMois,
    };
  }
}

enum StatutConge { enAttente, approuve, refuse, annule, rejete }

class Conge {
  final String id;
  final String agentId;
  final TypeConge type;
  final DateTime dateDebut;
  final DateTime dateFin;
  final int nombreJours;
  final String motif;
  final StatutConge statut;
  final String? commentaireRh;
  final String? approuvePar;
  final DateTime? dateApprobation;
  final bool isSynced;
  final DateTime createdAt;
  final DateTime updatedAt;

  Conge({
    required this.id,
    required this.agentId,
    required this.type,
    required this.dateDebut,
    required this.dateFin,
    required this.nombreJours,
    required this.motif,
    this.statut = StatutConge.enAttente,
    this.commentaireRh,
    this.approuvePar,
    this.dateApprobation,
    this.isSynced = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Conge.fromJson(Map<String, dynamic> json) {
    return Conge(
      id: json['id'],
      agentId: json['agent_id'],
      type: TypeConge.values[json['type']],
      dateDebut: DateTime.parse(json['date_debut']),
      dateFin: DateTime.parse(json['date_fin']),
      nombreJours: json['nombre_jours'],
      motif: json['motif'],
      statut: StatutConge.values[json['statut']],
      commentaireRh: json['commentaire_rh'],
      approuvePar: json['approuve_par'],
      dateApprobation:
          json['date_approbation'] != null
              ? DateTime.parse(json['date_approbation'])
              : null,
      isSynced: json['is_synced'] == 1,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agent_id': agentId,
      'type': type.index,
      'date_debut': dateDebut.toIso8601String(),
      'date_fin': dateFin.toIso8601String(),
      'nombre_jours': nombreJours,
      'motif': motif,
      'statut': statut.index,
      'commentaire_rh': commentaireRh,
      'approuve_par': approuvePar,
      'date_approbation': dateApprobation?.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get typeLabel {
    switch (type) {
      case TypeConge.congeAnnuel:
      case TypeConge.annuel:
        return 'Congé annuel';
      case TypeConge.congeMaladie:
      case TypeConge.maladie:
        return 'Congé maladie';
      case TypeConge.congeMaternite:
      case TypeConge.maternite:
        return 'Congé maternité';
      case TypeConge.congePaternite:
      case TypeConge.paternite:
        return 'Congé paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé sans traitement';
      case TypeConge.absence:
        return 'Absence';
    }
  }

  String get statutLabel {
    switch (statut) {
      case StatutConge.enAttente:
        return 'En attente';
      case StatutConge.approuve:
        return 'Approuvé';
      case StatutConge.refuse:
        return 'Refusé';
      case StatutConge.annule:
        return 'Annulé';
      case StatutConge.rejete:
        return 'Rejeté';
    }
  }

  String get periodeFormatee {
    final debut =
        '${dateDebut.day.toString().padLeft(2, '0')}/${dateDebut.month.toString().padLeft(2, '0')}/${dateDebut.year}';
    final fin =
        '${dateFin.day.toString().padLeft(2, '0')}/${dateFin.month.toString().padLeft(2, '0')}/${dateFin.year}';
    return '$debut - $fin';
  }

  bool get isEnCours {
    final maintenant = DateTime.now();
    return statut == StatutConge.approuve &&
        maintenant.isAfter(dateDebut) &&
        maintenant.isBefore(dateFin.add(const Duration(days: 1)));
  }

  bool isEnCoursADate(DateTime date) {
    return statut == StatutConge.approuve &&
        date.isAfter(dateDebut.subtract(const Duration(days: 1))) &&
        date.isBefore(dateFin.add(const Duration(days: 1)));
  }

  bool get isActive {
    final maintenant = DateTime.now();
    return statut == StatutConge.approuve &&
        maintenant.isAfter(dateDebut.subtract(const Duration(days: 1))) &&
        maintenant.isBefore(dateFin.add(const Duration(days: 1)));
  }

  bool get isFuture {
    final maintenant = DateTime.now();
    return dateDebut.isAfter(maintenant);
  }

  Conge copyWith({
    String? id,
    String? agentId,
    TypeConge? type,
    DateTime? dateDebut,
    DateTime? dateFin,
    int? nombreJours,
    String? motif,
    StatutConge? statut,
    String? commentaireRh,
    String? approuvePar,
    DateTime? dateApprobation,
    bool? isSynced,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Conge(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      type: type ?? this.type,
      dateDebut: dateDebut ?? this.dateDebut,
      dateFin: dateFin ?? this.dateFin,
      nombreJours: nombreJours ?? this.nombreJours,
      motif: motif ?? this.motif,
      statut: statut ?? this.statut,
      commentaireRh: commentaireRh ?? this.commentaireRh,
      approuvePar: approuvePar ?? this.approuvePar,
      dateApprobation: dateApprobation ?? this.dateApprobation,
      isSynced: isSynced ?? this.isSynced,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
