import 'package:flutter/foundation.dart';
import '../models/agent.dart';

class AuthService with ChangeNotifier {
  Agent? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;
  String? _error;

  // Getters
  Agent? get currentUser => _currentUser;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Méthodes d'authentification
  Future<bool> login(String email, String password) async {
    try {
      _setLoading(true);
      _clearError();
      
      // Simulation d'une authentification
      await Future.delayed(const Duration(seconds: 1));
      
      // TODO: Implémenter la vraie logique d'authentification
      _currentUser = Agent(
        id: '1',
        nom: 'Doe',
        prenom: 'John',
        email: email,
        telephone: '+33123456789',
        role: 'agent',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _isAuthenticated = true;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur de connexion: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _isAuthenticated = false;
    _clearError();
    notifyListeners();
  }

  Future<bool> register({
    required String nom,
    required String prenom,
    required String email,
    required String password,
    required String telephone,
  }) async {
    try {
      _setLoading(true);
      _clearError();
      
      // TODO: Implémenter la vraie logique d'inscription
      await Future.delayed(const Duration(seconds: 1));
      
      return true;
    } catch (e) {
      _setError('Erreur d\'inscription: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();
      
      // TODO: Implémenter la vraie logique de réinitialisation
      await Future.delayed(const Duration(seconds: 1));
      
      return true;
    } catch (e) {
      _setError('Erreur de réinitialisation: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> checkAuthStatus() async {
    try {
      _setLoading(true);
      
      // TODO: Vérifier le token stocké localement
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Simulation: pas d'utilisateur connecté au démarrage
      _isAuthenticated = false;
      _currentUser = null;
    } catch (e) {
      _setError('Erreur de vérification: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Méthodes privées
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}