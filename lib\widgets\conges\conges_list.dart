import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/providers/conge_provider.dart';
import '../../core/models/conge.dart';
import '../common/loading_widget.dart';
import '../common/empty_state_widget.dart';

class CongesList extends StatefulWidget {
  final bool showFilters;
  final VoidCallback? onCongeSelected;
  final Function(Conge)? onCongeEdit;
  final Function(Conge)? onCongeCancel;

  const CongesList({
    super.key,
    this.showFilters = true,
    this.onCongeSelected,
    this.onCongeEdit,
    this.onCongeCancel,
  });

  @override
  State<CongesList> createState() => _CongesListState();
}

class _CongesListState extends State<CongesList> {
  StatutConge? _selectedStatut;
  TypeConge? _selectedType;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CongeProvider>(
      builder: (context, congeProvider, child) {
        if (congeProvider.isLoading) {
          return const Center(child: LoadingWidget());
        }

        final filteredConges = _getFilteredConges(congeProvider.conges);

        return Column(
          children: [
            if (widget.showFilters) ...[
              _buildSearchBar(),
              const SizedBox(height: 12),
              _buildFilters(),
              const SizedBox(height: 16),
            ],
            Expanded(
              child: filteredConges.isEmpty
                  ? _buildEmptyState()
                  : _buildCongesList(filteredConges),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: TextField(
        controller: _searchController,
        decoration: const InputDecoration(
          hintText: 'Rechercher par motif...',
          prefixIcon: Icon(Icons.search),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildFilters() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatutFilter(),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildTypeFilter(),
          ),
          const SizedBox(width: 12),
          IconButton(
            onPressed: _clearFilters,
            icon: const Icon(Icons.clear_all),
            tooltip: 'Effacer les filtres',
          ),
        ],
      ),
    );
  }

  Widget _buildStatutFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<StatutConge?>(
          value: _selectedStatut,
          hint: const Text('Statut'),
          isExpanded: true,
          items: [
            const DropdownMenuItem<StatutConge?>(
              value: null,
              child: Text('Tous les statuts'),
            ),
            ...StatutConge.values.map((statut) {
              return DropdownMenuItem<StatutConge?>(
                value: statut,
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getStatutColor(statut),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(_getStatutLabel(statut)),
                  ],
                ),
              );
            }),
          ],
          onChanged: (value) {
            setState(() {
              _selectedStatut = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildTypeFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<TypeConge?>(
          value: _selectedType,
          hint: const Text('Type'),
          isExpanded: true,
          items: [
            const DropdownMenuItem<TypeConge?>(
              value: null,
              child: Text('Tous les types'),
            ),
            ...TypeConge.values.map((type) {
              return DropdownMenuItem<TypeConge?>(
                value: type,
                child: Row(
                  children: [
                    Icon(_getTypeIcon(type), size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _getTypeLabel(type),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
          onChanged: (value) {
            setState(() {
              _selectedType = value;
            });
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    String message;
    String subtitle;
    IconData icon;

    if (_searchQuery.isNotEmpty || _selectedStatut != null || _selectedType != null) {
      message = 'Aucun congé trouvé';
      subtitle = 'Essayez de modifier vos critères de recherche';
      icon = Icons.search_off;
    } else {
      message = 'Aucun congé enregistré';
      subtitle = 'Créez votre première demande de congé';
      icon = Icons.event_available;
    }

    return EmptyStateWidget(
      icon: icon,
      title: message,
      subtitle: subtitle,
      action: ElevatedButton(
        onPressed: () {
          // Navigation vers le formulaire de demande
          widget.onCongeSelected?.call();
        },
        child: const Text('Nouvelle demande'),
      ),
    );
  }

  Widget _buildCongesList(List<Conge> conges) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: conges.length,
      itemBuilder: (context, index) {
        final conge = conges[index];
        return _buildCongeCard(conge);
      },
    );
  }

  Widget _buildCongeCard(Conge conge) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showCongeDetails(conge),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getTypeColor(conge.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getTypeIcon(conge.type),
                      color: _getTypeColor(conge.type),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getTypeLabel(conge.type),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_formatDate(conge.dateDebut)} - ${_formatDate(conge.dateFin)}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatutChip(conge.statut),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${conge.nombreJours} jour${conge.nombreJours > 1 ? 's' : ''}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  if (conge.statut == StatutConge.enAttente) ...[
                    IconButton(
                      onPressed: () => widget.onCongeEdit?.call(conge),
                      icon: const Icon(Icons.edit, size: 20),
                      tooltip: 'Modifier',
                      constraints: const BoxConstraints(),
                      padding: const EdgeInsets.all(4),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => widget.onCongeCancel?.call(conge),
                      icon: const Icon(Icons.cancel, size: 20),
                      tooltip: 'Annuler',
                      constraints: const BoxConstraints(),
                      padding: const EdgeInsets.all(4),
                    ),
                  ],
                ],
              ),
              if (conge.motif.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    conge.motif,
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatutChip(StatutConge statut) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatutColor(statut).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getStatutColor(statut).withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        _getStatutLabel(statut),
        style: TextStyle(
          color: _getStatutColor(statut),
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  List<Conge> _getFilteredConges(List<Conge> conges) {
    return conges.where((conge) {
      // Filtre par recherche
      if (_searchQuery.isNotEmpty) {
        if (!conge.motif.toLowerCase().contains(_searchQuery)) {
          return false;
        }
      }

      // Filtre par statut
      if (_selectedStatut != null && conge.statut != _selectedStatut) {
        return false;
      }

      // Filtre par type
      if (_selectedType != null && conge.type != _selectedType) {
        return false;
      }

      return true;
    }).toList();
  }

  void _clearFilters() {
    setState(() {
      _selectedStatut = null;
      _selectedType = null;
      _searchQuery = '';
      _searchController.clear();
    });
  }

  void _showCongeDetails(Conge conge) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(_getTypeIcon(conge.type)),
            const SizedBox(width: 8),
            Expanded(child: Text(_getTypeLabel(conge.type))),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDetailRow('Statut', _getStatutLabel(conge.statut)),
              _buildDetailRow('Date de début', _formatDate(conge.dateDebut)),
              _buildDetailRow('Date de fin', _formatDate(conge.dateFin)),
              _buildDetailRow('Nombre de jours', '${conge.nombreJours}'),
              _buildDetailRow('Date de demande', _formatDate(conge.createdAt)),
              if (conge.motif.isNotEmpty)
                _buildDetailRow('Motif', conge.motif),
              if (conge.commentaireRh?.isNotEmpty == true)
                _buildDetailRow('Commentaire RH', conge.commentaireRh!),
              if (conge.dateApprobation != null)
                _buildDetailRow('Date d\'approbation', _formatDate(conge.dateApprobation!)),
            ],
          ),
        ),
        actions: [
          if (conge.statut == StatutConge.enAttente) ...[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onCongeEdit?.call(conge);
              },
              child: const Text('Modifier'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onCongeCancel?.call(conge);
              },
              child: const Text('Annuler'),
            ),
          ],
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _getTypeLabel(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return 'Congé annuel';
      case TypeConge.congeMaladie:
        return 'Congé maladie';
      case TypeConge.congeMaternite:
        return 'Congé maternité';
      case TypeConge.congePaternite:
        return 'Congé paternité';
      case TypeConge.congeSansTraitement:
        return 'Congé sans traitement';
      case TypeConge.absence:
        return 'Absence';
    }
  }

  String _getStatutLabel(StatutConge statut) {
    switch (statut) {
      case StatutConge.enAttente:
        return 'En attente';
      case StatutConge.approuve:
        return 'Approuvé';
      case StatutConge.refuse:
        return 'Refusé';
      case StatutConge.annule:
        return 'Annulé';
    }
  }

  IconData _getTypeIcon(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return Icons.beach_access;
      case TypeConge.congeMaladie:
        return Icons.local_hospital;
      case TypeConge.congeMaternite:
        return Icons.child_care;
      case TypeConge.congePaternite:
        return Icons.family_restroom;
      case TypeConge.congeSansTraitement:
        return Icons.money_off;
      case TypeConge.absence:
        return Icons.event_busy;
    }
  }

  Color _getTypeColor(TypeConge type) {
    switch (type) {
      case TypeConge.congeAnnuel:
        return Colors.blue;
      case TypeConge.congeMaladie:
        return Colors.red;
      case TypeConge.congeMaternite:
        return Colors.pink;
      case TypeConge.congePaternite:
        return Colors.indigo;
      case TypeConge.congeSansTraitement:
        return Colors.orange;
      case TypeConge.absence:
        return Colors.grey;
    }
  }

  Color _getStatutColor(StatutConge statut) {
    switch (statut) {
      case StatutConge.enAttente:
        return Colors.orange;
      case StatutConge.approuve:
        return Colors.green;
      case StatutConge.refuse:
        return Colors.red;
      case StatutConge.annule:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}