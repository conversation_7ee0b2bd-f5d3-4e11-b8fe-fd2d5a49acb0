import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  /// Vérifie et demande les permissions de localisation
  Future<LocationPermissionResult> checkAndRequestPermission() async {
    try {
      // Vérifier le statut du service de localisation
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return LocationPermissionResult(
          granted: false,
          errorMessage:
              'Le service de localisation est désactivé. Veuillez l\'activer dans les paramètres.',
          errorType: LocationErrorType.serviceDisabled,
        );
      }

      // Vérifier les permissions
      LocationPermission permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return LocationPermissionResult(
            granted: false,
            errorMessage: 'Permission de localisation refusée.',
            errorType: LocationErrorType.permissionDenied,
          );
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return LocationPermissionResult(
          granted: false,
          errorMessage:
              'Permission de localisation définitivement refusée. Veuillez l\'activer dans les paramètres.',
          errorType: LocationErrorType.permissionDeniedForever,
        );
      }

      return LocationPermissionResult(
        granted: true,
        errorMessage: null,
        errorType: null,
      );
    } catch (e) {
      return LocationPermissionResult(
        granted: false,
        errorMessage:
            'Erreur lors de la vérification des permissions: ${e.toString()}',
        errorType: LocationErrorType.unknown,
      );
    }
  }

  /// Obtient la position actuelle
  Future<LocationResult> getCurrentPosition() async {
    try {
      // Vérifier les permissions
      final permissionResult = await checkAndRequestPermission();
      if (!permissionResult.granted) {
        return LocationResult(
          success: false,
          position: null,
          address: null,
          errorMessage: permissionResult.errorMessage,
          errorType: permissionResult.errorType,
        );
      }

      // Obtenir la position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // Obtenir l'adresse
      String? address;
      try {
        List<Placemark> placemarks = await placemarkFromCoordinates(
          position.latitude,
          position.longitude,
        );
        if (placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          address =
              '${placemark.street ?? ''}, ${placemark.locality ?? ''}, ${placemark.country ?? ''}'
                  .trim();
          if (address.startsWith(',')) address = address.substring(1).trim();
          if (address.endsWith(','))
            address = address.substring(0, address.length - 1).trim();
        }
      } catch (e) {
        // Ignorer les erreurs de géocodage
        address = 'Adresse non disponible';
      }

      return LocationResult(
        success: true,
        position: position,
        address: address,
        errorMessage: null,
        errorType: null,
      );
    } catch (e) {
      String errorMessage;
      LocationErrorType errorType;

      if (e is TimeoutException) {
        errorMessage = 'Délai d\'attente dépassé pour obtenir la localisation';
        errorType = LocationErrorType.timeout;
      } else if (e.toString().contains('PERMISSION_DENIED')) {
        errorMessage = 'Permission de localisation refusée';
        errorType = LocationErrorType.permissionDenied;
      } else {
        errorMessage =
            'Erreur lors de l\'obtention de la localisation: ${e.toString()}';
        errorType = LocationErrorType.unknown;
      }

      return LocationResult(
        success: false,
        position: null,
        address: null,
        errorMessage: errorMessage,
        errorType: errorType,
      );
    }
  }

  /// Vérifie si la position est dans le périmètre autorisé
  bool isWithinAllowedArea(
    Position currentPosition,
    Position centerPosition,
    double radiusInMeters,
  ) {
    double distance = Geolocator.distanceBetween(
      currentPosition.latitude,
      currentPosition.longitude,
      centerPosition.latitude,
      centerPosition.longitude,
    );
    return distance <= radiusInMeters;
  }

  /// Calcule la distance entre deux positions
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  /// Obtient l'adresse à partir des coordonnées
  Future<String?> getAddressFromCoordinates(
    double latitude,
    double longitude,
  ) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        latitude,
        longitude,
      );
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        return '${placemark.street ?? ''}, ${placemark.locality ?? ''}, ${placemark.country ?? ''}'
            .trim();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Obtient les coordonnées à partir d'une adresse
  Future<Position?> getCoordinatesFromAddress(String address) async {
    try {
      List<Location> locations = await locationFromAddress(address);
      if (locations.isNotEmpty) {
        final location = locations.first;
        return Position(
          latitude: location.latitude,
          longitude: location.longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          heading: 0,
          speed: 0,
          speedAccuracy: 0,
          altitudeAccuracy: 0,
          headingAccuracy: 0,
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Surveille les changements de position
  Stream<Position> watchPosition() {
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Mise à jour tous les 10 mètres
    );
    return Geolocator.getPositionStream(locationSettings: locationSettings);
  }

  /// Ouvre les paramètres de localisation
  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  /// Ouvre les paramètres d'application
  Future<void> openAppSettings() async {
    await Geolocator.openAppSettings();
  }
}

/// Résultat de la vérification des permissions
class LocationPermissionResult {
  final bool granted;
  final String? errorMessage;
  final LocationErrorType? errorType;

  LocationPermissionResult({
    required this.granted,
    this.errorMessage,
    this.errorType,
  });
}

/// Résultat de l'obtention de la localisation
class LocationResult {
  final bool success;
  final Position? position;
  final String? address;
  final String? errorMessage;
  final LocationErrorType? errorType;

  LocationResult({
    required this.success,
    this.position,
    this.address,
    this.errorMessage,
    this.errorType,
  });

  @override
  String toString() {
    return 'LocationResult{success: $success, position: $position, address: $address, errorMessage: $errorMessage}';
  }
}

/// Types d'erreurs de localisation
enum LocationErrorType {
  serviceDisabled,
  permissionDenied,
  permissionDeniedForever,
  timeout,
  unknown,
}
