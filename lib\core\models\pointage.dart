enum TypePointage {
  entree,
  sortie,
  pauseDebut,
  pauseFin,
}

class Pointage {
  final String id;
  final String agentId;
  final TypePointage type;
  final DateTime dateHeure;
  final double latitude;
  final double longitude;
  final String? adresse;
  final bool isValidated;
  final String? commentaire;
  final bool isSynced;
  final DateTime createdAt;

  Pointage({
    required this.id,
    required this.agentId,
    required this.type,
    required this.dateHeure,
    required this.latitude,
    required this.longitude,
    this.adresse,
    this.isValidated = false,
    this.commentaire,
    this.isSynced = false,
    required this.createdAt,
  });

  factory Pointage.fromJson(Map<String, dynamic> json) {
    return Pointage(
      id: json['id'],
      agentId: json['agent_id'],
      type: TypePointage.values[json['type']],
      dateHeure: DateTime.parse(json['date_heure']),
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      adresse: json['adresse'],
      isValidated: json['is_validated'] ?? false,
      commentaire: json['commentaire'],
      isSynced: json['is_synced'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agent_id': agentId,
      'type': type.index,
      'date_heure': dateHeure.toIso8601String(),
      'latitude': latitude,
      'longitude': longitude,
      'adresse': adresse,
      'is_validated': isValidated,
      'commentaire': commentaire,
      'is_synced': isSynced,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get typeLabel {
    switch (type) {
      case TypePointage.entree:
        return 'Entrée';
      case TypePointage.sortie:
        return 'Sortie';
      case TypePointage.pauseDebut:
        return 'Début pause';
      case TypePointage.pauseFin:
        return 'Fin pause';
    }
  }

  String get heureFormatee {
    return '${dateHeure.hour.toString().padLeft(2, '0')}:${dateHeure.minute.toString().padLeft(2, '0')}';
  }

  Pointage copyWith({
    String? id,
    String? agentId,
    TypePointage? type,
    DateTime? dateHeure,
    double? latitude,
    double? longitude,
    String? adresse,
    bool? isValidated,
    String? commentaire,
    bool? isSynced,
    DateTime? createdAt,
  }) {
    return Pointage(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      type: type ?? this.type,
      dateHeure: dateHeure ?? this.dateHeure,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      adresse: adresse ?? this.adresse,
      isValidated: isValidated ?? this.isValidated,
      commentaire: commentaire ?? this.commentaire,
      isSynced: isSynced ?? this.isSynced,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}